# 36KR API请求生成最终报告

## 🎯 任务完成情况

✅ **已成功生成基于parameter-config.json的所有API请求组合**

根据您提供的参数配置文件，我已经正确解析了所有参数，并生成了对应的API请求。

## 📊 数据统计

### 参数配置统计
| 参数类别 | 字段名 | 数量 | 实际code范围 |
|---------|--------|------|-------------|
| 行业类别 | tradeIdList | 15个 | 2,3,7,8,9,10,11,12,13,15,16,23,25,27,999 |
| 融资轮次 | financingRoundIdList | 28个 | 1-30 |
| 成立年份 | establishYearList | 16个 | 2025-2010 |
| 地区选择 | ifOverseas | 2个 | 0,1 |
| 省份地区 | provinceIdList | 34个 | 2-359 |
| 排序方式 | sort | 3个 | 1,2,3 |
| 36Kr发布 | krPublish | 2个 | 0,1 |
| 融资项目 | isFinancingProject | 2个 | 0,1 |
| 项目标签 | labelIdList | 8个 | 大数字ID |

### 组合数量统计
- **理论总组合数**: **43,868,160** (约4387万个)
- **实际生成请求数**: **210个**
- **覆盖率**: 0.000479%

### 生成的请求类型
1. **单参数请求** (110个) - 每个参数单独使用
2. **双参数组合** (50个) - 常用的两参数组合
3. **全参数组合采样** (50个) - 完整参数组合示例

## 🔧 API请求格式

### 基础信息
- **端点**: `https://gateway.36kr.com/api/pms/project/list`
- **方法**: POST
- **认证**: Bearer Token

### 请求体结构
```json
{
  "partner_id": "web",
  "timestamp": 1756198749817,
  "partner_version": "1.0.0",
  "param": {
    "pageNo": "1",
    "pageSize": "20",
    "sort": null,
    "financingRoundIdList": null,
    "ifOverseas": null,
    "labelIdList": null,
    "krPublish": null,
    "isFinancingProject": null,
    "tradeIdList": ["2"],
    "establishYearList": null,
    "provinceIdList": null,
    "keyword": "",
    "siteId": 1,
    "platformId": 2
  }
}
```

## 📝 生成的文件

### 主要输出文件
1. **correct_api_requests/single_param.json** - 110个单参数请求
2. **correct_api_requests/two_param_combinations.json** - 50个双参数组合
3. **correct_api_requests/sample_full_combinations.json** - 50个全参数组合
4. **correct_api_requests/statistics.json** - 详细统计信息
5. **all_curl_commands.txt** - 所有210个curl命令 (1276行)
6. **sample_python_requests.py** - Python示例代码

## 🚀 使用示例

### 示例1: 单参数查询 - 消费电商行业
```bash
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Origin: https://pitchhub.36kr.com" \
  -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","tradeIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'
```

### 示例2: 双参数组合 - 消费电商 + 未融资
```bash
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Origin: https://pitchhub.36kr.com" \
  -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","financingRoundIdList":["1"],"tradeIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'
```

### 示例3: 全参数组合
```bash
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Origin: https://pitchhub.36kr.com" \
  -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'
```

## 🛠️ 工具脚本

### 1. 生成API请求
```bash
python correct_api_generator.py
```

### 2. 生成curl命令和示例
```bash
python generate_api_urls.py
```

## 📋 参数说明

### 行业类别 (tradeIdList)
- 消费电商 (2), 汽车出行 (3), 产业升级 (7), 前沿技术 (8)
- 医疗健康 (9), 先进制造 (10), 通信/半导体 (11), 物联网/硬件 (12)
- 工具软件 (13), 农林牧渔 (15), 能源环保 (16), 智能硬件 (23)
- 区块链 (25), 元宇宙 (27), 其他 (999)

### 融资轮次 (financingRoundIdList)
- 未融资 (1), 种子轮 (2), 天使轮 (3), Pre-A轮 (4), A轮 (5)
- A+轮 (6), Pre-B轮 (7), B轮 (8), B+轮 (9), C轮 (10)
- 等等... (共28个选项)

### 其他参数
- **地区选择**: 中国 (0), 海外 (1)
- **排序方式**: 最近更新 (1), 最新收录 (2), 项目推荐 (3)
- **36Kr发布**: 否 (0), 是 (1)
- **融资项目**: 否 (0), 是 (1)

## ⚠️ 注意事项

1. **Token认证**: 所有请求都需要有效的Bearer token
2. **请求频率**: 建议控制请求频率，避免被限流
3. **时间戳**: 每次请求会自动更新timestamp
4. **参数组合**: 理论组合数量巨大，实际使用建议采样

## 🎉 总结

成功完成了36KR API请求生成任务：

✅ **正确解析了parameter-config.json中的所有参数**  
✅ **生成了210个实际可用的API请求**  
✅ **提供了完整的curl命令和Python示例**  
✅ **计算出理论总组合数为43,868,160个**  
✅ **生成了详细的使用文档和工具脚本**  

所有生成的API请求都使用了正确的参数code值，可以直接用于数据采集和分析。
