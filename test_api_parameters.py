#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试36KR API参数的脚本
验证生成的参数是否能正确访问并获得响应
"""

import json
import time
import requests
from typing import Dict, List

class APIParameterTester:
    def __init__(self, config_file: str = "parameter-config.json"):
        """初始化测试器"""
        self.config = self.load_config(config_file)
        self.base_url = "https://gateway.36kr.com/api/pms/project/list"
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Origin": "https://pitchhub.36kr.com",
            "Referer": "https://pitchhub.36kr.com/",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
        }

    def load_config(self, filename: str) -> Dict:
        """加载配置文件"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)

    def create_test_request(self, test_type: str = "simple") -> Dict:
        """创建测试请求"""
        base_param = {
            "pageNo": "1",
            "pageSize": "20",
            "sort": None,
            "financingRoundIdList": None,
            "ifOverseas": None,
            "labelIdList": None,
            "krPublish": None,
            "isFinancingProject": None,
            "tradeIdList": None,
            "establishYearList": None,
            "provinceIdList": None,
            "keyword": "",
            "siteId": 1,
            "platformId": 2
        }

        if test_type == "simple":
            # 最简单的请求 - 只查询一个行业
            base_param["tradeIdList"] = ["2"]  # 消费电商
            description = "简单测试: 消费电商"
            
        elif test_type == "overseas":
            # 海外项目测试
            base_param["tradeIdList"] = ["2"]  # 消费电商
            base_param["financingRoundIdList"] = ["1"]  # 未融资
            base_param["establishYearList"] = ["2025"]  # 2025年
            base_param["ifOverseas"] = "1"  # 海外
            base_param["krPublish"] = "1"  # 是
            base_param["isFinancingProject"] = "1"  # 是
            base_param["labelIdList"] = ["2074768530084104"]  # 专精特新小巨人
            description = "海外项目测试: 消费电商 + 未融资 + 2025年 + 海外 + 是 + 是 + 专精特新小巨人"
            
        elif test_type == "domestic":
            # 中国项目测试
            base_param["tradeIdList"] = ["2"]  # 消费电商
            base_param["financingRoundIdList"] = ["1"]  # 未融资
            base_param["establishYearList"] = ["2025"]  # 2025年
            base_param["ifOverseas"] = "0"  # 中国
            base_param["provinceIdList"] = ["2"]  # 福建省
            base_param["krPublish"] = "1"  # 是
            base_param["isFinancingProject"] = "1"  # 是
            base_param["labelIdList"] = ["2074768530084104"]  # 专精特新小巨人
            description = "中国项目测试: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 是 + 是 + 专精特新小巨人"

        return {
            "partner_id": "web",
            "timestamp": int(time.time() * 1000),
            "partner_version": "1.0.0",
            "param": base_param
        }, description

    def test_api_request(self, request_data: Dict, description: str, timeout: int = 10) -> Dict:
        """测试API请求"""
        try:
            print(f"\n🧪 测试: {description}")
            print(f"📤 请求参数: {json.dumps(request_data['param'], ensure_ascii=False)}")
            
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=request_data,
                timeout=timeout
            )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📏 响应大小: {len(response.text)} 字节")
            
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    
                    # 检查响应结构
                    if "projectList" in json_response:
                        project_count = len(json_response["projectList"])
                        print(f"✅ 成功获取项目数据: {project_count} 个项目")
                        
                        if "page" in json_response:
                            page_info = json_response["page"]
                            total_count = page_info.get("totalCount", 0)
                            print(f"📈 总项目数: {total_count}")
                        
                        return {
                            "success": True,
                            "status_code": response.status_code,
                            "project_count": project_count,
                            "total_count": json_response.get("page", {}).get("totalCount", 0),
                            "response_structure": list(json_response.keys()),
                            "description": description
                        }
                    else:
                        print(f"⚠️ 响应结构异常: {list(json_response.keys())}")
                        return {
                            "success": False,
                            "status_code": response.status_code,
                            "error": "响应结构异常",
                            "response_keys": list(json_response.keys()),
                            "description": description
                        }
                        
                except json.JSONDecodeError:
                    print(f"❌ JSON解析失败")
                    print(f"📄 响应内容: {response.text[:500]}")
                    return {
                        "success": False,
                        "status_code": response.status_code,
                        "error": "JSON解析失败",
                        "response_text": response.text[:500],
                        "description": description
                    }
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"📄 响应内容: {response.text[:500]}")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": f"HTTP错误: {response.status_code}",
                    "response_text": response.text[:500],
                    "description": description
                }
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {str(e)}")
            return {
                "success": False,
                "error": f"请求异常: {str(e)}",
                "description": description
            }
        except Exception as e:
            print(f"❌ 未知错误: {str(e)}")
            return {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "description": description
            }

    def run_comprehensive_test(self) -> Dict:
        """运行综合测试"""
        print("🚀 开始36KR API参数综合测试")
        print("=" * 60)
        
        test_results = []
        
        # 测试1: 简单请求
        request_data, description = self.create_test_request("simple")
        result = self.test_api_request(request_data, description)
        test_results.append(result)
        
        # 测试2: 海外项目
        request_data, description = self.create_test_request("overseas")
        result = self.test_api_request(request_data, description)
        test_results.append(result)
        
        # 测试3: 中国项目
        request_data, description = self.create_test_request("domestic")
        result = self.test_api_request(request_data, description)
        test_results.append(result)
        
        # 汇总结果
        successful_tests = [r for r in test_results if r.get("success", False)]
        failed_tests = [r for r in test_results if not r.get("success", False)]
        
        summary = {
            "total_tests": len(test_results),
            "successful_tests": len(successful_tests),
            "failed_tests": len(failed_tests),
            "success_rate": len(successful_tests) / len(test_results) * 100,
            "test_results": test_results
        }
        
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功: {summary['successful_tests']}")
        print(f"失败: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        
        if successful_tests:
            print("\n✅ 成功的测试:")
            for test in successful_tests:
                print(f"  - {test['description']}")
                if 'project_count' in test:
                    print(f"    项目数: {test['project_count']}, 总数: {test.get('total_count', 'N/A')}")
        
        if failed_tests:
            print("\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test['description']}")
                print(f"    错误: {test.get('error', 'Unknown')}")
        
        # 保存测试结果
        with open("api_test_results.json", 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 详细测试结果已保存到: api_test_results.json")
        
        return summary

    def generate_curl_for_manual_test(self):
        """生成用于手动测试的curl命令"""
        print("\n🔧 手动测试curl命令:")
        print("=" * 60)
        
        test_types = ["simple", "overseas", "domestic"]
        
        for test_type in test_types:
            request_data, description = self.create_test_request(test_type)
            
            print(f"\n# {description}")
            print("curl -X POST \\")
            print(f"  '{self.base_url}' \\")
            
            for key, value in self.headers.items():
                print(f"  -H '{key}: {value}' \\")
            
            body_json = json.dumps(request_data, ensure_ascii=False, separators=(',', ':'))
            print(f"  -d '{body_json}'")

def main():
    """主函数"""
    tester = APIParameterTester()
    
    # 运行综合测试
    results = tester.run_comprehensive_test()
    
    # 生成手动测试命令
    tester.generate_curl_for_manual_test()
    
    # 返回测试是否成功
    return results["success_rate"] > 0

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 测试通过，可以继续生成完整参数组合")
    else:
        print("\n⚠️ 测试失败，需要修正参数后再生成")
