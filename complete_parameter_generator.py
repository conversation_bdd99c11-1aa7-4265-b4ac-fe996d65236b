#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的36KR API参数生成器
生成所有7,526,400个参数组合
基于测试验证的API格式
"""

import json
import time
import itertools
import os
import math
from typing import Dict, List, Iterator

class CompleteParameterGenerator:
    def __init__(self, config_file: str = "parameter-config.json"):
        """初始化生成器"""
        self.config = self.load_config(config_file)
        self.api_template = {
            "endpoint": "https://gateway.36kr.com/api/pms/project/list",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Origin": "https://pitchhub.36kr.com",
                "Referer": "https://pitchhub.36kr.com/",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
            },
            "body": {
                "partner_id": "web",
                "timestamp": 0,
                "partner_version": "1.0.0",
                "param": {
                    "pageNo": "1",
                    "pageSize": "20",
                    "sort": None,
                    "financingRoundIdList": None,
                    "ifOverseas": None,
                    "labelIdList": None,
                    "krPublish": None,
                    "isFinancingProject": None,
                    "tradeIdList": None,
                    "establishYearList": None,
                    "provinceIdList": None,
                    "keyword": "",
                    "siteId": 1,
                    "platformId": 2
                }
            }
        }

    def load_config(self, filename: str) -> Dict:
        """加载配置文件"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)

    def calculate_total_combinations(self) -> Dict[str, int]:
        """计算总组合数"""
        trade_count = len(self.config["tradeIdList"])
        financing_count = len(self.config["financingRoundIdList"])
        year_count = len(self.config["establishYearList"])
        kr_publish_count = len(self.config["krPublish"])
        financing_project_count = len(self.config["isFinancingProject"])
        label_count = len(self.config["labelIdList"])
        province_count = len(self.config["provinceIdList"])
        
        # 海外项目组合 (ifOverseas = 1, provinceIdList 不适用)
        overseas_combinations = (
            trade_count * financing_count * year_count * 
            kr_publish_count * financing_project_count * label_count
        )
        
        # 中国项目组合 (ifOverseas = 0, provinceIdList 有效)
        domestic_combinations = (
            trade_count * financing_count * year_count * province_count *
            kr_publish_count * financing_project_count * label_count
        )
        
        total_combinations = overseas_combinations + domestic_combinations
        
        return {
            "overseas_combinations": overseas_combinations,
            "domestic_combinations": domestic_combinations,
            "total_combinations": total_combinations,
            "parameter_counts": {
                "tradeIdList": trade_count,
                "financingRoundIdList": financing_count,
                "establishYearList": year_count,
                "provinceIdList": province_count,
                "krPublish": kr_publish_count,
                "isFinancingProject": financing_project_count,
                "labelIdList": label_count
            }
        }

    def generate_overseas_combinations(self) -> Iterator[Dict]:
        """生成海外项目组合 (ifOverseas = 1)"""
        count = 0
        
        for combo in itertools.product(
            self.config["tradeIdList"],
            self.config["financingRoundIdList"],
            self.config["establishYearList"],
            self.config["krPublish"],
            self.config["isFinancingProject"],
            self.config["labelIdList"]
        ):
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            
            # 设置参数
            request["body"]["param"]["tradeIdList"] = [str(combo[0]["code"])]
            request["body"]["param"]["financingRoundIdList"] = [str(combo[1]["code"])]
            request["body"]["param"]["establishYearList"] = [str(combo[2]["code"])]
            request["body"]["param"]["ifOverseas"] = "1"  # 海外
            request["body"]["param"]["provinceIdList"] = None  # 海外项目不设置省份
            request["body"]["param"]["krPublish"] = str(combo[3]["code"])
            request["body"]["param"]["isFinancingProject"] = str(combo[4]["code"])
            request["body"]["param"]["labelIdList"] = [str(combo[5]["code"])]
            
            # 生成描述
            desc_parts = [item["name"] for item in combo]
            request["description"] = f"海外项目: {' + '.join(desc_parts)}"
            request["category"] = "overseas"
            request["combination_id"] = f"overseas_{count + 1:06d}"
            
            yield request
            count += 1

    def generate_domestic_combinations(self) -> Iterator[Dict]:
        """生成中国项目组合 (ifOverseas = 0)"""
        count = 0
        
        for combo in itertools.product(
            self.config["tradeIdList"],
            self.config["financingRoundIdList"],
            self.config["establishYearList"],
            self.config["provinceIdList"],
            self.config["krPublish"],
            self.config["isFinancingProject"],
            self.config["labelIdList"]
        ):
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            
            # 设置参数
            request["body"]["param"]["tradeIdList"] = [str(combo[0]["code"])]
            request["body"]["param"]["financingRoundIdList"] = [str(combo[1]["code"])]
            request["body"]["param"]["establishYearList"] = [str(combo[2]["code"])]
            request["body"]["param"]["ifOverseas"] = "0"  # 中国
            request["body"]["param"]["provinceIdList"] = [str(combo[3]["code"])]
            request["body"]["param"]["krPublish"] = str(combo[4]["code"])
            request["body"]["param"]["isFinancingProject"] = str(combo[5]["code"])
            request["body"]["param"]["labelIdList"] = [str(combo[6]["code"])]
            
            # 生成描述
            desc_parts = [item["name"] for item in combo]
            request["description"] = f"中国项目: {' + '.join(desc_parts)}"
            request["category"] = "domestic"
            request["combination_id"] = f"domestic_{count + 1:07d}"
            
            yield request
            count += 1

    def save_all_combinations(self, chunk_size: int = 5000, output_dir: str = "all_api_combinations"):
        """保存所有组合到文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 计算总数
        stats = self.calculate_total_combinations()
        
        print(f"🚀 开始生成完整的API参数组合")
        print(f"海外项目组合数: {stats['overseas_combinations']:,}")
        print(f"中国项目组合数: {stats['domestic_combinations']:,}")
        print(f"总组合数: {stats['total_combinations']:,}")
        print(f"分块大小: {chunk_size:,}")
        print(f"预计文件数: {math.ceil(stats['total_combinations'] / chunk_size):,}")
        print("=" * 60)
        
        # 生成海外项目组合
        print(f"\n📦 生成海外项目组合...")
        overseas_count = 0
        chunk_num = 1
        current_chunk = []
        
        for request in self.generate_overseas_combinations():
            current_chunk.append(request)
            overseas_count += 1
            
            if len(current_chunk) >= chunk_size:
                filename = os.path.join(output_dir, f"overseas_chunk_{chunk_num:04d}.json")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(current_chunk, f, ensure_ascii=False, indent=2)
                print(f"✅ 已保存海外组合块 {chunk_num}: {len(current_chunk)} 个请求")
                current_chunk = []
                chunk_num += 1
        
        # 保存剩余的海外组合
        if current_chunk:
            filename = os.path.join(output_dir, f"overseas_chunk_{chunk_num:04d}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(current_chunk, f, ensure_ascii=False, indent=2)
            print(f"✅ 已保存海外组合块 {chunk_num}: {len(current_chunk)} 个请求")
        
        overseas_chunks = chunk_num
        
        # 生成中国项目组合
        print(f"\n📦 生成中国项目组合...")
        domestic_count = 0
        chunk_num = 1
        current_chunk = []
        
        for request in self.generate_domestic_combinations():
            current_chunk.append(request)
            domestic_count += 1
            
            if len(current_chunk) >= chunk_size:
                filename = os.path.join(output_dir, f"domestic_chunk_{chunk_num:04d}.json")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(current_chunk, f, ensure_ascii=False, indent=2)
                print(f"✅ 已保存中国组合块 {chunk_num}: {len(current_chunk)} 个请求")
                current_chunk = []
                chunk_num += 1
            
            # 显示进度
            if domestic_count % 50000 == 0:
                progress = domestic_count / stats['domestic_combinations'] * 100
                print(f"📈 中国项目进度: {domestic_count:,}/{stats['domestic_combinations']:,} ({progress:.1f}%)")
        
        # 保存剩余的中国组合
        if current_chunk:
            filename = os.path.join(output_dir, f"domestic_chunk_{chunk_num:04d}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(current_chunk, f, ensure_ascii=False, indent=2)
            print(f"✅ 已保存中国组合块 {chunk_num}: {len(current_chunk)} 个请求")
        
        domestic_chunks = chunk_num
        
        # 保存统计信息和清单
        final_stats = {
            **stats,
            "generated_overseas": overseas_count,
            "generated_domestic": domestic_count,
            "total_generated": overseas_count + domestic_count,
            "chunk_size": chunk_size,
            "overseas_chunks": overseas_chunks,
            "domestic_chunks": domestic_chunks,
            "total_chunks": overseas_chunks + domestic_chunks,
            "generation_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        stats_filename = os.path.join(output_dir, "generation_statistics.json")
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(final_stats, f, ensure_ascii=False, indent=2)
        
        # 生成文件清单
        manifest = {
            "total_files": overseas_chunks + domestic_chunks,
            "overseas_files": [f"overseas_chunk_{i:04d}.json" for i in range(1, overseas_chunks + 1)],
            "domestic_files": [f"domestic_chunk_{i:04d}.json" for i in range(1, domestic_chunks + 1)],
            "statistics_file": "generation_statistics.json"
        }
        
        manifest_filename = os.path.join(output_dir, "file_manifest.json")
        with open(manifest_filename, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎉 生成完成!")
        print(f"实际生成海外组合: {overseas_count:,}")
        print(f"实际生成中国组合: {domestic_count:,}")
        print(f"总计生成: {overseas_count + domestic_count:,}")
        print(f"海外文件块数: {overseas_chunks}")
        print(f"中国文件块数: {domestic_chunks}")
        print(f"总文件数: {overseas_chunks + domestic_chunks}")
        print(f"统计信息: {stats_filename}")
        print(f"文件清单: {manifest_filename}")

def main():
    """主函数"""
    print("36KR API完整参数组合生成器")
    print("基于测试验证的API格式")
    print("=" * 50)
    
    generator = CompleteParameterGenerator()
    
    # 显示计算结果
    stats = generator.calculate_total_combinations()
    print(f"参数组合计算结果:")
    print(f"海外项目组合数: {stats['overseas_combinations']:,}")
    print(f"中国项目组合数: {stats['domestic_combinations']:,}")
    print(f"总组合数: {stats['total_combinations']:,}")
    
    # 直接开始生成
    print(f"\n🚀 开始生成 {stats['total_combinations']:,} 个API请求参数")
    print(f"预计文件大小: 约 {stats['total_combinations'] * 2 / 1024:.1f} MB")

    # 生成所有组合
    generator.save_all_combinations(chunk_size=5000)

if __name__ == "__main__":
    main()
