#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的36KR API请求生成器
基于parameter-config.json中的实际参数生成API请求
"""

import json
import time
import itertools
from typing import Dict, List, Iterator

class CorrectAPIRequestGenerator:
    def __init__(self, config_file: str = "parameter-config.json"):
        """初始化生成器"""
        self.config = self.load_config(config_file)
        
        # API模板
        self.api_template = {
            "endpoint": "https://gateway.36kr.com/api/pms/project/list",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer {token}",
                "Origin": "https://pitchhub.36kr.com",
                "Referer": "https://pitchhub.36kr.com/"
            },
            "body": {
                "partner_id": "web",
                "timestamp": 0,
                "partner_version": "1.0.0",
                "param": {
                    "pageNo": "1",
                    "pageSize": "20",
                    "sort": None,
                    "financingRoundIdList": None,
                    "ifOverseas": None,
                    "labelIdList": None,
                    "krPublish": None,
                    "isFinancingProject": None,
                    "tradeIdList": None,
                    "establishYearList": None,
                    "provinceIdList": None,
                    "keyword": "",
                    "siteId": 1,
                    "platformId": 2
                }
            }
        }

    def load_config(self, filename: str) -> Dict:
        """加载配置文件"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)

    def generate_single_param_requests(self) -> List[Dict]:
        """生成单参数请求"""
        requests = []
        
        # 行业类别
        for item in self.config["tradeIdList"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["tradeIdList"] = [str(item["code"])]
            request["description"] = f"行业: {item['name']}"
            requests.append(request)
        
        # 融资轮次
        for item in self.config["financingRoundIdList"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["financingRoundIdList"] = [str(item["code"])]
            request["description"] = f"融资轮次: {item['name']}"
            requests.append(request)
        
        # 成立年份
        for item in self.config["establishYearList"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["establishYearList"] = [str(item["code"])]
            request["description"] = f"成立年份: {item['name']}"
            requests.append(request)
        
        # 地区选择
        for item in self.config["ifOverseas"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["ifOverseas"] = str(item["code"])
            request["description"] = f"地区: {item['name']}"
            requests.append(request)
        
        # 省份
        for item in self.config["provinceIdList"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["provinceIdList"] = [str(item["code"])]
            request["description"] = f"省份: {item['name']}"
            requests.append(request)
        
        # 排序
        for item in self.config["sort"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["sort"] = str(item["code"])
            request["description"] = f"排序: {item['name']}"
            requests.append(request)
        
        # 36Kr发布
        for item in self.config["krPublish"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["krPublish"] = str(item["code"])
            request["description"] = f"36Kr发布: {item['name']}"
            requests.append(request)
        
        # 融资项目
        for item in self.config["isFinancingProject"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["isFinancingProject"] = str(item["code"])
            request["description"] = f"融资项目: {item['name']}"
            requests.append(request)
        
        # 项目标签
        for item in self.config["labelIdList"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["labelIdList"] = [str(item["code"])]
            request["description"] = f"项目标签: {item['name']}"
            requests.append(request)
        
        return requests

    def generate_two_param_combinations(self) -> List[Dict]:
        """生成两参数组合"""
        requests = []
        
        # 行业 + 融资轮次 (前5个组合)
        for i, industry in enumerate(self.config["tradeIdList"][:5]):
            for j, financing in enumerate(self.config["financingRoundIdList"][:5]):
                request = json.loads(json.dumps(self.api_template))
                request["body"]["timestamp"] = int(time.time() * 1000)
                request["body"]["param"]["tradeIdList"] = [str(industry["code"])]
                request["body"]["param"]["financingRoundIdList"] = [str(financing["code"])]
                request["description"] = f"行业+融资: {industry['name']} + {financing['name']}"
                requests.append(request)
        
        # 地区 + 成立时间 (前5个组合)
        for i, province in enumerate(self.config["provinceIdList"][:5]):
            for j, year in enumerate(self.config["establishYearList"][:5]):
                request = json.loads(json.dumps(self.api_template))
                request["body"]["timestamp"] = int(time.time() * 1000)
                request["body"]["param"]["provinceIdList"] = [str(province["code"])]
                request["body"]["param"]["establishYearList"] = [str(year["code"])]
                request["description"] = f"地区+年份: {province['name']} + {year['name']}"
                requests.append(request)
        
        return requests

    def calculate_total_combinations(self) -> int:
        """计算理论总组合数"""
        total = (
            len(self.config["tradeIdList"]) *
            len(self.config["financingRoundIdList"]) *
            len(self.config["establishYearList"]) *
            len(self.config["ifOverseas"]) *
            len(self.config["provinceIdList"]) *
            len(self.config["sort"]) *
            len(self.config["krPublish"]) *
            len(self.config["isFinancingProject"]) *
            len(self.config["labelIdList"])
        )
        return total

    def generate_sample_full_combinations(self, sample_size: int = 50) -> List[Dict]:
        """生成全参数组合的采样"""
        requests = []
        count = 0
        
        # 使用前几个参数进行组合，避免组合爆炸
        trade_sample = self.config["tradeIdList"][:3]
        financing_sample = self.config["financingRoundIdList"][:3]
        year_sample = self.config["establishYearList"][:2]
        overseas_sample = self.config["ifOverseas"]
        province_sample = self.config["provinceIdList"][:2]
        sort_sample = self.config["sort"]
        kr_sample = self.config["krPublish"]
        financing_project_sample = self.config["isFinancingProject"]
        label_sample = self.config["labelIdList"][:2]
        
        for combo in itertools.product(
            trade_sample, financing_sample, year_sample, overseas_sample,
            province_sample, sort_sample, kr_sample, financing_project_sample, label_sample
        ):
            if count >= sample_size:
                break
                
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            
            # 设置参数
            request["body"]["param"]["tradeIdList"] = [str(combo[0]["code"])]
            request["body"]["param"]["financingRoundIdList"] = [str(combo[1]["code"])]
            request["body"]["param"]["establishYearList"] = [str(combo[2]["code"])]
            request["body"]["param"]["ifOverseas"] = str(combo[3]["code"])
            request["body"]["param"]["provinceIdList"] = [str(combo[4]["code"])]
            request["body"]["param"]["sort"] = str(combo[5]["code"])
            request["body"]["param"]["krPublish"] = str(combo[6]["code"])
            request["body"]["param"]["isFinancingProject"] = str(combo[7]["code"])
            request["body"]["param"]["labelIdList"] = [str(combo[8]["code"])]
            
            # 生成描述
            desc_parts = [item["name"] for item in combo]
            request["description"] = f"全参数组合: {' + '.join(desc_parts)}"
            
            requests.append(request)
            count += 1
        
        return requests

    def generate_all_requests(self) -> Dict[str, List[Dict]]:
        """生成所有类型的请求"""
        return {
            "single_param": self.generate_single_param_requests(),
            "two_param_combinations": self.generate_two_param_combinations(),
            "sample_full_combinations": self.generate_sample_full_combinations()
        }

    def save_requests(self, output_dir: str = "correct_api_requests"):
        """保存所有请求到文件"""
        import os
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        all_requests = self.generate_all_requests()
        
        # 保存各类请求
        for category, requests in all_requests.items():
            filename = os.path.join(output_dir, f"{category}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
            print(f"已生成 {len(requests)} 个 {category} 请求，保存到 {filename}")
        
        # 生成统计信息
        total_requests = sum(len(requests) for requests in all_requests.values())
        total_combinations = self.calculate_total_combinations()
        
        stats = {
            "total_generated_requests": total_requests,
            "total_possible_combinations": total_combinations,
            "categories": {category: len(requests) for category, requests in all_requests.items()},
            "parameter_counts": {
                "tradeIdList": len(self.config["tradeIdList"]),
                "financingRoundIdList": len(self.config["financingRoundIdList"]),
                "establishYearList": len(self.config["establishYearList"]),
                "ifOverseas": len(self.config["ifOverseas"]),
                "provinceIdList": len(self.config["provinceIdList"]),
                "sort": len(self.config["sort"]),
                "krPublish": len(self.config["krPublish"]),
                "isFinancingProject": len(self.config["isFinancingProject"]),
                "labelIdList": len(self.config["labelIdList"])
            }
        }
        
        stats_filename = os.path.join(output_dir, "statistics.json")
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n统计信息:")
        print(f"实际生成请求数: {total_requests}")
        print(f"理论总组合数: {total_combinations:,}")
        print(f"统计信息已保存到: {stats_filename}")

def main():
    """主函数"""
    print("正确的36KR API请求生成器")
    print("=" * 50)
    
    generator = CorrectAPIRequestGenerator()
    generator.save_requests()
    
    print("\n生成完成！")

if __name__ == "__main__":
    main()
