{"total_tests": 3, "successful_tests": 0, "failed_tests": 3, "success_rate": 0.0, "test_results": [{"success": false, "status_code": 200, "error": "响应结构异常", "response_keys": ["code", "data"], "description": "简单测试: 消费电商"}, {"success": false, "status_code": 200, "error": "响应结构异常", "response_keys": ["code", "data"], "description": "海外项目测试: 消费电商 + 未融资 + 2025年 + 海外 + 是 + 是 + 专精特新小巨人"}, {"success": false, "status_code": 200, "error": "响应结构异常", "response_keys": ["code", "data"], "description": "中国项目测试: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 是 + 是 + 专精特新小巨人"}]}