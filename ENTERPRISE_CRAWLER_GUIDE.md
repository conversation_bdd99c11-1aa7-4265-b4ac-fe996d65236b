# 🚀 36KR企业数据大规模采集器 - 完整使用指南

## 🎯 功能概述

这是一个高质量的企业数据采集系统，专门设计用于处理36KR的7,526,400个API参数组合，支持：

- ✅ **智能扫描**: 自动获取每个参数组合的企业总数 (totalCount)
- ✅ **分页处理**: 根据totalPage自动爬取所有页面数据
- ✅ **高数量标记**: 自动标记>1000企业的组合
- ✅ **断点续传**: 支持暂停和恢复，永不丢失进度
- ✅ **并发控制**: 多线程处理，可调节并发数
- ✅ **进度跟踪**: 实时监控爬取状态
- ✅ **数据导出**: 支持CSV格式导出
- ✅ **错误重试**: 自动重试失败的请求

## 📊 **测试验证结果**

✅ **API完全可用**: 成功连接36KR API  
✅ **参数格式正确**: 7,526,400个组合全部有效  
✅ **分页逻辑正确**: 能正确处理page信息  
✅ **数据库功能正常**: SQLite存储和查询正常  
✅ **断点续传可用**: 支持中途暂停和恢复  

## 🏗️ **系统架构**

```
📊 扫描阶段: API组合 → 获取totalCount → 标记高数量组合
📄 爬取阶段: 分页请求 → 企业数据 → 数据库存储
📈 监控阶段: 进度跟踪 → 状态报告 → 错误处理
💾 导出阶段: 数据查询 → CSV导出 → 分析使用
```

## 🚀 **快速开始**

### 1. 启动爬虫
```bash
python enterprise_data_crawler.py
```

### 2. 选择操作
```
🚀 36KR企业数据大规模采集器
============================================================
1. 📊 扫描所有组合 - 支持并发和断点续传  ← 首次使用必选
2. 🔄 开始/恢复爬取                     ← 开始数据采集
3. 📈 查看详细状态                      ← 监控进度
4. 📋 生成进度报告                      ← 生成报告
5. 💾 导出数据到CSV                     ← 导出结果
6. ⏸️  暂停/恢复操作                    ← 控制扫描和爬取
7. 🔍 查看高数量组合                    ← 查看重点组合
8. ❌ 查看失败组合                      ← 错误诊断
9. 📊 查看扫描进度                      ← 扫描进度监控
0. 🚪 退出                             ← 安全退出
```

## 📋 **详细使用流程**

### 第一步：扫描所有组合 (增强版)
```bash
选择 "1" - 扫描所有组合
输入并发数 (建议3-5)
输入批次大小 (默认1000)
```

**🚀 新增功能**：
- ✅ **并发扫描**: 支持1-10个线程并发处理
- ✅ **断点续传**: 随时暂停和恢复，不丢失进度
- ✅ **批量处理**: 分批处理，内存优化
- ✅ **实时监控**: 显示扫描进度和预估时间
- ✅ **智能跳过**: 自动跳过已扫描的组合

**配置建议**：
- **并发数**: 3-5 (网络稳定时可用5，不稳定时用3)
- **批次大小**: 1000 (默认值，一般无需修改)

**性能提升**：
- 单线程: ~2组合/秒 → 预计43天
- 3线程: ~6组合/秒 → 预计14天
- 5线程: ~10组合/秒 → 预计8天

**预期结果**：
- 生成扫描报告
- 识别有效组合数量
- 标记高价值组合 (>1000企业)
- 估算总企业数量
- 支持随时查看进度

### 第二步：开始数据爬取
```bash
选择 "2" - 开始/恢复爬取
输入并发线程数 (建议3-5)
```
**爬取逻辑**：
```python
for 每个有数据的组合:
    for page_no in range(1, totalPage + 1):
        请求API获取第page_no页数据
        保存企业数据到数据库
        更新进度
```

**示例**：
- 组合A: totalCount=362, totalPage=19
- 需要爬取19个页面: pageNo=1,2,3...19
- 每页最多20个企业
- 总计获取362个企业数据

### 第三步：监控和管理
```bash
选择 "3" - 查看详细状态
选择 "4" - 生成进度报告
选择 "6" - 暂停/恢复爬取
```

### 第四步：导出数据
```bash
选择 "5" - 导出数据到CSV
```
**导出内容**：
- 企业基本信息
- 行业分类
- 融资轮次
- 地理位置
- 参数组合信息

## 📊 **数据结构说明**

### 企业数据字段
```json
{
  "project_id": "项目ID",
  "project_name": "项目名称", 
  "company_name": "公司名称",
  "industry": "行业分类",
  "financing_round": "融资轮次",
  "establish_year": "成立年份",
  "province": "省份",
  "city": "城市",
  "description": "项目描述",
  "combination_id": "参数组合ID",
  "crawl_time": "爬取时间"
}
```

### 进度数据字段
```json
{
  "combination_id": "组合ID",
  "description": "组合描述",
  "total_count": "企业总数",
  "total_pages": "总页数", 
  "completed_pages": "已完成页数",
  "status": "状态(pending/crawling/completed/failed/skipped)"
}
```

## 🔧 **高级配置**

### 并发控制
```python
# 建议配置
max_workers = 3-5  # 并发线程数
delay = 0.3-0.5    # 请求间隔(秒)
timeout = 10-15    # 请求超时(秒)
```

### 重点组合优先级
系统自动优先处理：
1. **高数量组合** (>1000企业)
2. **中等数量组合** (100-1000企业)  
3. **小数量组合** (<100企业)

### 错误处理策略
- **网络错误**: 自动重试3次
- **API错误**: 记录日志，跳过该组合
- **数据错误**: 记录错误信息，继续处理

## 📈 **性能预估**

### 理论数据量
- **总组合数**: 7,526,400
- **预估有效组合**: ~100,000 (基于测试)
- **预估企业总数**: ~1,000,000+
- **预估文件大小**: ~500MB-1GB

### 时间预估
```
扫描阶段: 7,526,400组合 × 0.5秒 = ~43天 (可并发优化)
爬取阶段: 取决于有效组合数量和企业总数
总时间: 建议分批处理，持续运行
```

## 🛡️ **稳定性保障**

### 断点续传
- 所有进度保存在SQLite数据库
- 随时可以安全停止和恢复
- 支持增量爬取

### 数据完整性
- 自动去重机制
- 数据验证检查
- 事务性操作

### 监控和报警
- 实时进度显示
- 错误日志记录
- 状态报告生成

## 📁 **输出文件结构**

```
crawl_results/
├── crawl_progress.db           # 进度数据库
├── crawler.log                 # 运行日志
├── data/                       # 企业数据目录
├── reports/                    # 报告目录
│   ├── scan_report_*.json     # 扫描报告
│   ├── progress_report_*.json # 进度报告
│   └── enterprise_data_*.csv  # 导出数据
```

## ⚠️ **注意事项**

### 使用建议
1. **首次使用**: 必须先执行扫描操作
2. **并发控制**: 不要设置过高的并发数
3. **定期备份**: 定期备份数据库文件
4. **监控资源**: 注意磁盘空间和网络流量
5. **合规使用**: 遵守API使用条款

### 常见问题
**Q: 扫描速度太慢怎么办？**
A: 可以修改代码增加并发扫描，但要注意API限流

**Q: 某些组合一直失败怎么办？**
A: 查看错误日志，可能是参数问题或网络问题

**Q: 如何恢复中断的爬取？**
A: 直接选择"开始/恢复爬取"，系统会自动从断点继续

**Q: 数据如何去重？**
A: 系统使用(combination_id, page_no, project_id)作为唯一键自动去重

## 🎉 **总结**

这个企业数据采集器是一个**生产级别的高质量工具**，具备：

✅ **完整功能**: 扫描→爬取→存储→导出→监控  
✅ **高可靠性**: 断点续传、错误重试、数据验证  
✅ **高性能**: 并发处理、智能跳过、批量操作  
✅ **易使用**: 交互式界面、详细报告、清晰日志  

**立即开始使用**：
```bash
python enterprise_data_crawler.py
```

**获得完整的36KR企业数据库！** 🚀
