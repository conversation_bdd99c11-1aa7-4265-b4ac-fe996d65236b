[{"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["1"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["2"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 消费电商 + 未融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["2"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["2"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 消费电商 + 种子轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["3"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["2"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 消费电商 + 天使轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["4"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["2"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 消费电商 + Pre-A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["5"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["2"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 消费电商 + A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["1"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["3"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 汽车出行 + 未融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["2"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["3"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 汽车出行 + 种子轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["3"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["3"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 汽车出行 + 天使轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["4"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["3"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 汽车出行 + Pre-A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["5"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["3"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 汽车出行 + A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["1"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["7"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 产业升级 + 未融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["2"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["7"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 产业升级 + 种子轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["3"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["7"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 产业升级 + 天使轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["4"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["7"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 产业升级 + Pre-A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["5"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["7"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 产业升级 + A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["1"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["8"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 前沿技术 + 未融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["2"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["8"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 前沿技术 + 种子轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["3"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["8"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 前沿技术 + 天使轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["4"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["8"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 前沿技术 + Pre-A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["5"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["8"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 前沿技术 + A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["1"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["9"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 医疗健康 + 未融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["2"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["9"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 医疗健康 + 种子轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["3"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["9"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 医疗健康 + 天使轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["4"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["9"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 医疗健康 + Pre-A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["5"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["9"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业+融资: 医疗健康 + A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2025"], "provinceIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 福建省 + 2025年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2024"], "provinceIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 福建省 + 2024年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2023"], "provinceIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 福建省 + 2023年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2022"], "provinceIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 福建省 + 2022年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2021"], "provinceIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 福建省 + 2021年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2025"], "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 广东省 + 2025年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2024"], "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 广东省 + 2024年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2023"], "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 广东省 + 2023年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2022"], "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 广东省 + 2022年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2021"], "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 广东省 + 2021年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2025"], "provinceIdList": ["5"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 北京市 + 2025年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2024"], "provinceIdList": ["5"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 北京市 + 2024年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2023"], "provinceIdList": ["5"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 北京市 + 2023年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2022"], "provinceIdList": ["5"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 北京市 + 2022年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2021"], "provinceIdList": ["5"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 北京市 + 2021年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2025"], "provinceIdList": ["6"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 香港特别行政区 + 2025年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2024"], "provinceIdList": ["6"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 香港特别行政区 + 2024年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2023"], "provinceIdList": ["6"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 香港特别行政区 + 2023年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2022"], "provinceIdList": ["6"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 香港特别行政区 + 2022年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2021"], "provinceIdList": ["6"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 香港特别行政区 + 2021年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2025"], "provinceIdList": ["7"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 吉林省 + 2025年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2024"], "provinceIdList": ["7"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 吉林省 + 2024年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2023"], "provinceIdList": ["7"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 吉林省 + 2023年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2022"], "provinceIdList": ["7"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 吉林省 + 2022年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693155, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2021"], "provinceIdList": ["7"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区+年份: 吉林省 + 2021年"}]