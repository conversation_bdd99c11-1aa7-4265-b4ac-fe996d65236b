# 36KR API完整参数组合分析报告

## 📊 参数配置分析

### 参数统计
| 参数名 | 数量 | 说明 |
|--------|------|------|
| 行业分类 | 15个 | tradeIdList |
| 融资轮次 | 28个 | financingRoundIdList |
| 成立年份 | 16个 | establishYearList |
| 地域选择 | 2个 | ifOverseas |
| 省份选择 | 34个 | provinceIdList |
| 36氪发布状态 | 2个 | krPublish |
| 融资项目状态 | 2个 | isFinancingProject |
| 项目标签 | 8个 | labelIdList |

### 🔗 参数依赖关系

**关键发现**: ifOverseas与provinceIdList存在互斥关系

- **海外项目** (ifOverseas=1): provinceIdList参数不适用
- **中国项目** (ifOverseas=0): provinceIdList参数有34个省份选项

## 🧮 组合数量计算

### 海外项目组合
- **数量**: 215,040
- **公式**: 15 × 28 × 16 × 2 × 2 × 8
- **说明**: 海外项目组合数（不包含省份参数）

### 中国项目组合
- **数量**: 7,311,360
- **公式**: 15 × 28 × 16 × 34 × 2 × 2 × 8
- **说明**: 中国项目组合数（包含省份参数）

### 总组合数
- **总计**: 7,526,400
- **公式**: 215040 + 7311360

## 🚀 API请求示例

生成了 50 个示例请求:
- 海外项目示例: 20 个
- 中国项目示例: 30 个

### 示例请求格式

```json
{
  "endpoint": "https://gateway.36kr.com/api/pms/project/list",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer {token}",
    "Origin": "https://pitchhub.36kr.com",
    "Referer": "https://pitchhub.36kr.com/"
  },
  "body": {
    "partner_id": "web",
    "timestamp": 1756199598440,
    "partner_version": "1.0.0",
    "param": {
      "pageNo": "1",
      "pageSize": "20",
      "sort": null,
      "financingRoundIdList": [
        "1"
      ],
      "ifOverseas": "1",
      "labelIdList": [
        "2074768530084104"
      ],
      "krPublish": "1",
      "isFinancingProject": "1",
      "tradeIdList": [
        "2"
      ],
      "establishYearList": [
        "2025"
      ],
      "provinceIdList": null,
      "keyword": "",
      "siteId": 1,
      "platformId": 2
    }
  },
  "description": "海外项目: 消费电商 + 未融资 + 2025年 + 是 + 是 + 专精特新小巨人",
  "category": "overseas"
}
```

## 📋 使用说明

1. **查看参数分析**: `parameter_analysis.json`
2. **使用示例请求**: `sample_requests.json`
3. **执行curl命令**: `sample_curl_commands.txt`
4. **理解依赖关系**: 注意ifOverseas与provinceIdList的互斥关系

## ⚠️ 重要提醒

- 总组合数量巨大，实际使用时建议采样
- 所有请求都需要有效的Bearer token
- 建议控制请求频率，避免被限流
