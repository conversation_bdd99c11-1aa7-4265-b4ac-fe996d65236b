[{"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["1"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 文化娱乐"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["2"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 消费电商"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["3"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 汽车出行"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["4"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 教育"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["5"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 金融"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["6"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 企业服务"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["7"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 产业升级"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["8"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 前沿技术"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["9"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 医疗健康"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["10"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 先进制造"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["11"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 通信/半导体"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["12"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 物联网/硬件"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["13"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 工具软件"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["14"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 社交网络"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["15"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 农林牧渔"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["16"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 能源环保"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["17"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 本地生活"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["18"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 体育游戏"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["19"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 跨境出海"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["20"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 房产地产"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["21"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 旅游"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["22"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 广告营销"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["23"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 智能硬件"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["24"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 物流"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["25"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 区块链"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["26"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 传统制造"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["27"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 元宇宙"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["999"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 其他"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["1"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 未融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["2"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 种子轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["3"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 天使轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["4"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: Pre-A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["5"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["6"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: A+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["7"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: Pre-B轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["8"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: B轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["9"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: B+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["10"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: C轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["11"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: C+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["12"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: D轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["13"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: D+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["14"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: E轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["16"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: F轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["18"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: G轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["19"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: H轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["24"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 股权融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["21"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 战略融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["26"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 定向增发"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["20"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: Pre-IPO"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["27"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 基石轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["25"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 已上市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["23"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: IPO"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["28"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 新三板"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["29"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 已退市/私有化"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["30"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 并购/合并"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229393, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 其他"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2025"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2025年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2024"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2024年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2023"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2023年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2022"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2022年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2021"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2021年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2020"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2020年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2019"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2019年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2018"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2018年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2017"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2017年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2016"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2016年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2015"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2015年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2014"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2014年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2013"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2013年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2012"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2012年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2011年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 2010年及以前"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": "0", "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 中国"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": "1", "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 海外"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 福建省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 广东省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["5"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 北京市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["6"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 香港特别行政区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["7"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 吉林省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["9"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 天津市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["10"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 辽宁省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["13"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 上海市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["14"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 河北省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["16"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 江苏省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["18"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 内蒙古自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["31"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 台湾省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["38"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 贵州省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["46"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 宁夏回族自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["52"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 浙江省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["56"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 安徽省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["59"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 山东省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["63"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 黑龙江省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["77"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 山西省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["86"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 陕西省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["112"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 广西壮族自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["115"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 河南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["152"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 重庆市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["156"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 四川省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["159"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 云南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["174"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 澳门特别行政区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["175"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 湖北省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["192"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 西藏自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["232"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 甘肃省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["247"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 海南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["287"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 新疆维吾尔自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["301"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 青海省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["321"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 湖南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["359"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 江西省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": "3", "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 项目推荐"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": "1", "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 最近更新"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": "2", "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 最新收录"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": "1", "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 是"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": "0", "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 否"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": "1", "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 是"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": "0", "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 否"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530084104"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 专精特新小巨人"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530018562"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 专精特新"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2076252066686472"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 创新型中小企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530280704"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 高新技术企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530297091"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 科技型中小企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530198787"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 独角兽"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530215175"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 瞪羚企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198229394, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530247942"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "单参数查询: 雏鹰企业"}]