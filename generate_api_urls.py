#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成API请求URL和curl命令的脚本
"""

import json
import time
from typing import Dict, List

def load_requests(directory: str = "correct_api_requests") -> Dict[str, List[Dict]]:
    """加载生成的API请求"""
    import os
    
    requests = {}
    files = ["single_param.json", "two_param_combinations.json", "sample_full_combinations.json"]
    
    for file in files:
        filepath = os.path.join(directory, file)
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                category = file.replace('.json', '')
                requests[category] = json.load(f)
    
    return requests

def generate_curl_command(request: Dict, include_token: bool = False) -> str:
    """生成curl命令"""
    # 更新时间戳
    request["body"]["timestamp"] = int(time.time() * 1000)
    
    headers = []
    for key, value in request["headers"].items():
        if key == "Authorization" and not include_token:
            headers.append(f'-H "{key}: Bearer YOUR_TOKEN_HERE"')
        else:
            headers.append(f'-H "{key}: {value}"')
    
    body_json = json.dumps(request["body"], ensure_ascii=False, separators=(',', ':'))
    
    curl_cmd = f"""curl -X {request["method"]} \\
  '{request["endpoint"]}' \\
  {' '.join(headers)} \\
  -d '{body_json}'"""
    
    return curl_cmd

def generate_python_requests_code(request: Dict) -> str:
    """生成Python requests代码"""
    # 更新时间戳
    request["body"]["timestamp"] = int(time.time() * 1000)
    
    code = f"""import requests
import json

url = "{request["endpoint"]}"
headers = {json.dumps(request["headers"], indent=2)}
data = {json.dumps(request["body"], indent=2, ensure_ascii=False)}

# 记得替换YOUR_TOKEN_HERE为实际的token
headers["Authorization"] = "Bearer YOUR_TOKEN_HERE"

response = requests.post(url, headers=headers, json=data)
print(f"状态码: {{response.status_code}}")
print(f"响应: {{response.json()}}")"""
    
    return code

def print_request_summary(requests: Dict[str, List[Dict]]):
    """打印请求摘要"""
    print("API请求生成摘要")
    print("=" * 50)
    
    total_requests = 0
    for category, request_list in requests.items():
        count = len(request_list)
        total_requests += count
        print(f"{category}: {count} 个请求")
    
    print(f"总计: {total_requests} 个请求")
    print()

def show_sample_requests(requests: Dict[str, List[Dict]], samples_per_category: int = 3):
    """显示示例请求"""
    print("API请求示例")
    print("=" * 50)
    
    for category, request_list in requests.items():
        print(f"\n【{category}】类别示例:")
        print("-" * 30)
        
        for i, request in enumerate(request_list[:samples_per_category]):
            print(f"\n示例 {i+1}: {request['description']}")
            
            # 显示关键参数
            params = request['body']['param']
            active_params = {}
            for key, value in params.items():
                if value is not None and value != "" and key not in ["pageNo", "pageSize", "keyword", "siteId", "platformId"]:
                    active_params[key] = value
            
            if active_params:
                print("参数:")
                for key, value in active_params.items():
                    print(f"  {key}: {value}")
            
            # 生成curl命令
            curl_cmd = generate_curl_command(request.copy())
            print(f"\nCurl命令:")
            print(curl_cmd)
            print()

def save_all_curl_commands(requests: Dict[str, List[Dict]], output_file: str = "all_curl_commands.txt"):
    """保存所有curl命令到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("36KR API 所有curl命令\n")
        f.write("=" * 50 + "\n\n")
        
        for category, request_list in requests.items():
            f.write(f"【{category}】\n")
            f.write("-" * 30 + "\n\n")
            
            for i, request in enumerate(request_list):
                f.write(f"# {i+1}. {request['description']}\n")
                curl_cmd = generate_curl_command(request.copy())
                f.write(curl_cmd + "\n\n")
            
            f.write("\n")
    
    print(f"所有curl命令已保存到: {output_file}")

def save_sample_python_code(requests: Dict[str, List[Dict]], output_file: str = "sample_python_requests.py"):
    """保存示例Python代码"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('#!/usr/bin/env python3\n')
        f.write('# -*- coding: utf-8 -*-\n')
        f.write('"""\n36KR API请求示例代码\n"""\n\n')
        
        # 选择几个代表性的请求
        sample_requests = []
        for category, request_list in requests.items():
            if request_list:
                sample_requests.append(request_list[0])  # 取每个类别的第一个
        
        for i, request in enumerate(sample_requests[:5]):  # 最多5个示例
            f.write(f"# 示例 {i+1}: {request['description']}\n")
            f.write("def example_" + str(i+1) + "():\n")
            code = generate_python_requests_code(request.copy())
            # 缩进代码
            indented_code = '\n'.join('    ' + line for line in code.split('\n'))
            f.write(indented_code + "\n\n")
        
        f.write('if __name__ == "__main__":\n')
        f.write('    # 运行示例（取消注释想要运行的示例）\n')
        for i in range(min(5, len(sample_requests))):
            f.write(f'    # example_{i+1}()\n')
    
    print(f"示例Python代码已保存到: {output_file}")

def main():
    """主函数"""
    print("36KR API请求URL生成器")
    print("=" * 50)
    
    # 加载请求
    try:
        requests = load_requests()
        if not requests:
            print("错误: 没有找到API请求文件")
            print("请先运行 correct_api_generator.py 生成请求")
            return
    except Exception as e:
        print(f"加载请求文件时出错: {e}")
        return
    
    # 显示摘要
    print_request_summary(requests)
    
    # 显示示例
    show_sample_requests(requests, samples_per_category=2)
    
    # 保存所有curl命令
    save_all_curl_commands(requests)
    
    # 保存示例Python代码
    save_sample_python_code(requests)
    
    # 计算总组合数
    try:
        with open("correct_api_requests/statistics.json", 'r', encoding='utf-8') as f:
            stats = json.load(f)
        
        print(f"\n总结:")
        print(f"实际生成的API请求数: {stats['total_generated_requests']}")
        print(f"理论总组合数: {stats['total_possible_combinations']:,}")
        print(f"覆盖率: {stats['total_generated_requests'] / stats['total_possible_combinations'] * 100:.6f}%")
        
    except Exception as e:
        print(f"读取统计信息时出错: {e}")

if __name__ == "__main__":
    main()
