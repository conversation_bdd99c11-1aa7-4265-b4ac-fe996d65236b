# 36KR API请求生成总结报告

## 概述
基于 `parameter-config.json` 配置文件，成功生成了36KR项目搜索API的所有可能请求组合。

## 参数配置摘要

| 参数类别 | 字段名 | 数量 | 说明 |
|---------|--------|------|------|
| 行业类别 | tradeIdList | 15个 | 消费电商、汽车出行、产业升级等 |
| 融资轮次 | financingRoundIdList | 28个 | 未融资、种子轮、天使轮、A轮等 |
| 成立年份 | establishYearList | 16个 | 2025年到2010年及以前 |
| 地区选择 | ifOverseas | 2个 | 中国、海外 |
| 省份地区 | provinceIdList | 34个 | 各省市自治区 |
| 排序方式 | sort | 3个 | 项目推荐、最近更新、最新收录 |
| 36Kr发布 | krPublish | 2个 | 是、否 |
| 融资项目 | isFinancingProject | 2个 | 是、否 |
| 项目标签 | labelIdList | 8个 | 专精特新、高新技术企业等 |

## 组合数量统计

### 理论总组合数
**43,868,160** (约4387万个组合)

计算公式: 15 × 28 × 16 × 2 × 34 × 3 × 2 × 2 × 8 = 43,868,160

### 实际生成的请求数量
- **单参数请求**: 123个 (每个参数单独使用)
- **常见组合**: 58个 (常用的参数组合)
- **复杂组合**: 100个 (多参数组合示例)
- **全量组合**: 1,000个 (从理论总数中采样)

**总计**: 1,281个实际生成的API请求

## API请求结构

### 基础信息
- **端点**: `https://gateway.36kr.com/api/pms/project/list`
- **方法**: POST
- **内容类型**: application/json

### 请求头
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {token}",
  "Origin": "https://pitchhub.36kr.com",
  "Referer": "https://pitchhub.36kr.com/"
}
```

### 请求体结构
```json
{
  "partner_id": "web",
  "timestamp": 1756198323878,
  "partner_version": "1.0.0",
  "param": {
    "pageNo": "1",
    "pageSize": "20",
    "sort": null,
    "financingRoundIdList": null,
    "ifOverseas": null,
    "labelIdList": null,
    "krPublish": null,
    "isFinancingProject": null,
    "tradeIdList": null,
    "establishYearList": null,
    "provinceIdList": null,
    "keyword": "",
    "siteId": 1,
    "platformId": 2
  }
}
```

## 示例API请求

### 示例1: 行业筛选 - 消费电商
```bash
curl -X POST \
  https://gateway.36kr.com/api/pms/project/list \
  -H "Content-Type: application/json" \
  -H "Origin: https://pitchhub.36kr.com" \
  -H "Referer: https://pitchhub.36kr.com/" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"partner_id": "web", "timestamp": 1756198323878, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "tradeIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}'
```

### 示例2: 多参数组合 - 产业升级 + 种子轮 + 广东省
```bash
curl -X POST \
  https://gateway.36kr.com/api/pms/project/list \
  -H "Content-Type: application/json" \
  -H "Origin: https://pitchhub.36kr.com" \
  -H "Referer: https://pitchhub.36kr.com/" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"partner_id": "web", "timestamp": 1756198323880, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": "3", "financingRoundIdList": ["2"], "krPublish": "1", "tradeIdList": ["7"], "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}'
```

## 生成的文件

1. **api_requests/single_param_requests.json** - 单参数请求 (123个)
2. **api_requests/common_combinations.json** - 常见组合 (58个)
3. **api_requests/complex_combinations.json** - 复杂组合 (100个)
4. **api_requests/full_combinations_part_*.json** - 全量组合分片 (1000个)
5. **api_examples.json** - 精选示例 (5个)
6. **api_requests/statistics.json** - 统计信息

## 使用说明

### 1. 查看生成的请求
```bash
# 查看单参数请求
cat api_requests/single_param_requests.json

# 查看统计信息
cat api_requests/statistics.json
```

### 2. 测试API请求
```bash
# 生成curl命令
python test_api_requests.py
# 选择选项1生成curl命令

# 或直接测试API (需要token)
python test_api_requests.py
# 选择选项2并输入token
```

### 3. 重新生成请求
```bash
# 生成基础请求
python api_generator.py

# 生成全量组合 (谨慎使用，数量巨大)
FULL_COMBINATIONS=1 python api_generator.py
```

## 注意事项

1. **Token认证**: 所有API请求都需要有效的Bearer token
2. **请求频率**: 建议控制请求频率，避免被限流
3. **数据量**: 理论组合数量巨大(4387万)，实际使用时建议采样
4. **时间戳**: 每次请求都会自动更新timestamp为当前时间

## 总结

成功生成了36KR项目搜索API的完整请求组合，包括：
- **理论总组合数**: 43,868,160个
- **实际生成请求**: 1,281个
- **参数类别**: 9种主要参数
- **测试工具**: 完整的测试和验证脚本

所有生成的请求都遵循36KR API的标准格式，可以直接用于数据采集和分析。
