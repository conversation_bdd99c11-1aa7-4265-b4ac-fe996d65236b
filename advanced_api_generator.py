#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级36KR API请求生成器
基于API参数组合分析指导，考虑参数依赖关系
"""

import json
import time
import itertools
import os
from typing import Dict, List, Iterator, Tuple

class AdvancedAPIRequestGenerator:
    def __init__(self, config_file: str = "parameter-config.json"):
        """初始化生成器"""
        self.config = self.load_config(config_file)
        self.api_template = {
            "endpoint": "https://gateway.36kr.com/api/pms/project/list",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer {token}",
                "Origin": "https://pitchhub.36kr.com",
                "Referer": "https://pitchhub.36kr.com/"
            },
            "body": {
                "partner_id": "web",
                "timestamp": 0,
                "partner_version": "1.0.0",
                "param": {
                    "pageNo": "1",
                    "pageSize": "20",
                    "sort": None,
                    "financingRoundIdList": None,
                    "ifOverseas": None,
                    "labelIdList": None,
                    "krPublish": None,
                    "isFinancingProject": None,
                    "tradeIdList": None,
                    "establishYearList": None,
                    "provinceIdList": None,
                    "keyword": "",
                    "siteId": 1,
                    "platformId": 2
                }
            }
        }

    def load_config(self, filename: str) -> Dict:
        """加载配置文件"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)

    def calculate_total_combinations(self) -> Dict[str, int]:
        """计算总组合数，考虑参数依赖关系"""
        # 基础参数数量
        trade_count = len(self.config["tradeIdList"])
        financing_count = len(self.config["financingRoundIdList"])
        year_count = len(self.config["establishYearList"])
        kr_publish_count = len(self.config["krPublish"])
        financing_project_count = len(self.config["isFinancingProject"])
        label_count = len(self.config["labelIdList"])
        province_count = len(self.config["provinceIdList"])

        # 情况1：海外项目 (ifOverseas = 1, provinceIdList 不适用)
        overseas_combinations = (
            trade_count * financing_count * year_count *
            kr_publish_count * financing_project_count * label_count
        )

        # 情况2：中国项目 (ifOverseas = 0, provinceIdList 有效)
        domestic_combinations = (
            trade_count * financing_count * year_count * province_count *
            kr_publish_count * financing_project_count * label_count
        )

        total_combinations = overseas_combinations + domestic_combinations

        return {
            "overseas_combinations": overseas_combinations,
            "domestic_combinations": domestic_combinations,
            "total_combinations": total_combinations,
            "parameter_counts": {
                "tradeIdList": trade_count,
                "financingRoundIdList": financing_count,
                "establishYearList": year_count,
                "provinceIdList": province_count,
                "krPublish": kr_publish_count,
                "isFinancingProject": financing_project_count,
                "labelIdList": label_count
            }
        }

    def generate_overseas_combinations(self, limit: int = None) -> Iterator[Dict]:
        """生成海外项目组合 (ifOverseas = 1)"""
        count = 0

        for combo in itertools.product(
            self.config["tradeIdList"],
            self.config["financingRoundIdList"],
            self.config["establishYearList"],
            self.config["krPublish"],
            self.config["isFinancingProject"],
            self.config["labelIdList"]
        ):
            if limit and count >= limit:
                break

            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)

            # 设置参数
            request["body"]["param"]["tradeIdList"] = [str(combo[0]["code"])]
            request["body"]["param"]["financingRoundIdList"] = [str(combo[1]["code"])]
            request["body"]["param"]["establishYearList"] = [str(combo[2]["code"])]
            request["body"]["param"]["ifOverseas"] = "1"  # 海外
            request["body"]["param"]["provinceIdList"] = None  # 海外项目不设置省份
            request["body"]["param"]["krPublish"] = str(combo[3]["code"])
            request["body"]["param"]["isFinancingProject"] = str(combo[4]["code"])
            request["body"]["param"]["labelIdList"] = [str(combo[5]["code"])]

            # 生成描述
            desc_parts = [item["name"] for item in combo]
            request["description"] = f"海外项目: {' + '.join(desc_parts)}"
            request["category"] = "overseas"

            yield request
            count += 1

    def generate_domestic_combinations(self, limit: int = None) -> Iterator[Dict]:
        """生成中国项目组合 (ifOverseas = 0)"""
        count = 0

        for combo in itertools.product(
            self.config["tradeIdList"],
            self.config["financingRoundIdList"],
            self.config["establishYearList"],
            self.config["provinceIdList"],
            self.config["krPublish"],
            self.config["isFinancingProject"],
            self.config["labelIdList"]
        ):
            if limit and count >= limit:
                break

            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)

            # 设置参数
            request["body"]["param"]["tradeIdList"] = [str(combo[0]["code"])]
            request["body"]["param"]["financingRoundIdList"] = [str(combo[1]["code"])]
            request["body"]["param"]["establishYearList"] = [str(combo[2]["code"])]
            request["body"]["param"]["ifOverseas"] = "0"  # 中国
            request["body"]["param"]["provinceIdList"] = [str(combo[3]["code"])]
            request["body"]["param"]["krPublish"] = str(combo[4]["code"])
            request["body"]["param"]["isFinancingProject"] = str(combo[5]["code"])
            request["body"]["param"]["labelIdList"] = [str(combo[6]["code"])]

            # 生成描述
            desc_parts = [item["name"] for item in combo]
            request["description"] = f"中国项目: {' + '.join(desc_parts)}"
            request["category"] = "domestic"

            yield request
            count += 1

    def generate_sample_combinations(self, overseas_samples: int = 100, domestic_samples: int = 100) -> Dict[str, List[Dict]]:
        """生成采样组合"""
        overseas_requests = list(self.generate_overseas_combinations(limit=overseas_samples))
        domestic_requests = list(self.generate_domestic_combinations(limit=domestic_samples))
        
        return {
            "overseas_samples": overseas_requests,
            "domestic_samples": domestic_requests
        }

    def generate_single_param_requests(self) -> List[Dict]:
        """生成单参数请求"""
        requests = []
        
        # 行业类别
        for item in self.config["tradeIdList"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["tradeIdList"] = [str(item["code"])]
            request["description"] = f"行业: {item['name']}"
            request["category"] = "single_param"
            requests.append(request)
        
        # 融资轮次
        for item in self.config["financingRoundIdList"]:
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            request["body"]["param"]["financingRoundIdList"] = [str(item["code"])]
            request["description"] = f"融资轮次: {item['name']}"
            request["category"] = "single_param"
            requests.append(request)
        
        # 其他单参数...（省略部分代码以节省空间）
        
        return requests

    def save_combinations_to_chunks(self, chunk_size: int = 1000, max_chunks: int = 10, output_dir: str = "advanced_api_requests"):
        """分块保存组合到文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 计算总数
        stats = self.calculate_total_combinations()
        
        print(f"开始生成API请求组合...")
        print(f"海外项目组合数: {stats['overseas_combinations']:,}")
        print(f"中国项目组合数: {stats['domestic_combinations']:,}")
        print(f"总组合数: {stats['total_combinations']:,}")
        
        # 生成海外项目组合
        overseas_count = 0
        chunk_num = 1
        current_chunk = []
        
        print(f"\n生成海外项目组合...")
        for request in self.generate_overseas_combinations(limit=chunk_size * max_chunks // 2):
            current_chunk.append(request)
            overseas_count += 1
            
            if len(current_chunk) >= chunk_size:
                filename = os.path.join(output_dir, f"overseas_combinations_chunk_{chunk_num:03d}.json")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(current_chunk, f, ensure_ascii=False, indent=2)
                print(f"已保存海外组合块 {chunk_num}: {len(current_chunk)} 个请求")
                current_chunk = []
                chunk_num += 1
        
        # 保存剩余的海外组合
        if current_chunk:
            filename = os.path.join(output_dir, f"overseas_combinations_chunk_{chunk_num:03d}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(current_chunk, f, ensure_ascii=False, indent=2)
            print(f"已保存海外组合块 {chunk_num}: {len(current_chunk)} 个请求")
        
        # 生成中国项目组合
        domestic_count = 0
        chunk_num = 1
        current_chunk = []
        
        print(f"\n生成中国项目组合...")
        for request in self.generate_domestic_combinations(limit=chunk_size * max_chunks // 2):
            current_chunk.append(request)
            domestic_count += 1
            
            if len(current_chunk) >= chunk_size:
                filename = os.path.join(output_dir, f"domestic_combinations_chunk_{chunk_num:03d}.json")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(current_chunk, f, ensure_ascii=False, indent=2)
                print(f"已保存中国组合块 {chunk_num}: {len(current_chunk)} 个请求")
                current_chunk = []
                chunk_num += 1
        
        # 保存剩余的中国组合
        if current_chunk:
            filename = os.path.join(output_dir, f"domestic_combinations_chunk_{chunk_num:03d}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(current_chunk, f, ensure_ascii=False, indent=2)
            print(f"已保存中国组合块 {chunk_num}: {len(current_chunk)} 个请求")
        
        # 保存统计信息
        stats["generated_overseas"] = overseas_count
        stats["generated_domestic"] = domestic_count
        stats["total_generated"] = overseas_count + domestic_count
        
        stats_filename = os.path.join(output_dir, "advanced_statistics.json")
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n生成完成!")
        print(f"实际生成海外组合: {overseas_count:,}")
        print(f"实际生成中国组合: {domestic_count:,}")
        print(f"总计生成: {overseas_count + domestic_count:,}")
        print(f"统计信息已保存到: {stats_filename}")

def main():
    """主函数"""
    print("高级36KR API请求生成器")
    print("基于参数依赖关系分析")
    print("=" * 50)
    
    generator = AdvancedAPIRequestGenerator()
    
    # 显示计算结果
    stats = generator.calculate_total_combinations()
    print(f"参数组合分析结果:")
    print(f"海外项目组合数: {stats['overseas_combinations']:,}")
    print(f"中国项目组合数: {stats['domestic_combinations']:,}")
    print(f"总组合数: {stats['total_combinations']:,}")
    
    # 生成组合
    generator.save_combinations_to_chunks(chunk_size=1000, max_chunks=20)

if __name__ == "__main__":
    main()
