#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第一个JSON文件的API请求
演示如何开始使用生成的API参数组合
"""

import json
import requests
import time
from typing import Dict, List

class APIChunkTester:
    def __init__(self):
        """初始化测试器"""
        self.session = requests.Session()
        # 设置通用headers
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        })

    def load_chunk_file(self, filename: str) -> List[Dict]:
        """加载chunk文件"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ 文件不存在: {filename}")
            return []
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            return []

    def test_single_request(self, api_request: Dict, timeout: int = 10) -> Dict:
        """测试单个API请求"""
        try:
            # 更新时间戳
            api_request["body"]["timestamp"] = int(time.time() * 1000)
            
            print(f"🧪 测试请求: {api_request.get('description', 'Unknown')}")
            print(f"📋 组合ID: {api_request.get('combination_id', 'N/A')}")
            print(f"📂 类别: {api_request.get('category', 'N/A')}")
            
            # 显示关键参数
            params = api_request["body"]["param"]
            key_params = {}
            for key, value in params.items():
                if value is not None and value != "" and key not in ["pageNo", "pageSize", "keyword", "siteId", "platformId"]:
                    key_params[key] = value
            print(f"🔧 关键参数: {json.dumps(key_params, ensure_ascii=False)}")
            
            # 发送请求
            response = self.session.post(
                api_request["endpoint"],
                headers=api_request["headers"],
                json=api_request["body"],
                timeout=timeout
            )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📏 响应大小: {len(response.text)} 字节")
            
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    
                    if "code" in json_response and "data" in json_response:
                        code = json_response["code"]
                        data = json_response["data"]
                        
                        if code == 0:  # 成功
                            if isinstance(data, dict) and "projectList" in data:
                                project_count = len(data["projectList"])
                                total_count = data.get("page", {}).get("totalCount", 0)
                                
                                print(f"✅ 成功获取数据!")
                                print(f"📈 当前页项目数: {project_count}")
                                print(f"📊 总项目数: {total_count}")
                                
                                # 显示第一个项目信息
                                if project_count > 0:
                                    first_project = data["projectList"][0]
                                    print(f"📝 示例项目: {first_project.get('name', 'N/A')}")
                                    print(f"🏢 公司: {first_project.get('companyName', 'N/A')}")
                                
                                return {
                                    "success": True,
                                    "status_code": response.status_code,
                                    "response_code": code,
                                    "project_count": project_count,
                                    "total_count": total_count,
                                    "sample_project": first_project.get('name', 'N/A') if project_count > 0 else None,
                                    "description": api_request.get('description', '')
                                }
                            else:
                                print(f"⚠️ 数据结构异常: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                                return {
                                    "success": False,
                                    "status_code": response.status_code,
                                    "response_code": code,
                                    "error": "数据结构异常",
                                    "description": api_request.get('description', '')
                                }
                        else:
                            print(f"❌ API返回错误: code={code}")
                            print(f"📄 错误信息: {data}")
                            return {
                                "success": False,
                                "status_code": response.status_code,
                                "response_code": code,
                                "error": f"API错误: {data}",
                                "description": api_request.get('description', '')
                            }
                    else:
                        print(f"⚠️ 响应格式异常: {list(json_response.keys())}")
                        return {
                            "success": False,
                            "status_code": response.status_code,
                            "error": "响应格式异常",
                            "description": api_request.get('description', '')
                        }
                        
                except json.JSONDecodeError:
                    print(f"❌ JSON解析失败")
                    print(f"📄 响应内容: {response.text[:200]}...")
                    return {
                        "success": False,
                        "status_code": response.status_code,
                        "error": "JSON解析失败",
                        "description": api_request.get('description', '')
                    }
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"📄 响应内容: {response.text[:200]}...")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": f"HTTP错误: {response.status_code}",
                    "description": api_request.get('description', '')
                }
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {str(e)}")
            return {
                "success": False,
                "error": f"请求异常: {str(e)}",
                "description": api_request.get('description', '')
            }
        except Exception as e:
            print(f"❌ 未知错误: {str(e)}")
            return {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "description": api_request.get('description', '')
            }

    def test_chunk_sample(self, filename: str, sample_size: int = 5) -> Dict:
        """测试chunk文件的样本"""
        print(f"🚀 开始测试文件: {filename}")
        print("=" * 60)
        
        # 加载文件
        requests_list = self.load_chunk_file(filename)
        if not requests_list:
            return {"error": "无法加载文件"}
        
        print(f"📁 文件包含 {len(requests_list)} 个API请求")
        print(f"🧪 将测试前 {min(sample_size, len(requests_list))} 个请求")
        print()
        
        test_results = []
        
        for i in range(min(sample_size, len(requests_list))):
            print(f"\n--- 测试 {i+1}/{min(sample_size, len(requests_list))} ---")
            
            api_request = requests_list[i]
            result = self.test_single_request(api_request)
            test_results.append(result)
            
            print("-" * 40)
            
            # 避免请求过于频繁
            if i < min(sample_size, len(requests_list)) - 1:
                time.sleep(1)
        
        # 汇总结果
        successful = [r for r in test_results if r.get("success", False)]
        failed = [r for r in test_results if not r.get("success", False)]
        
        summary = {
            "filename": filename,
            "total_requests_in_file": len(requests_list),
            "tested_requests": len(test_results),
            "successful_tests": len(successful),
            "failed_tests": len(failed),
            "success_rate": len(successful) / len(test_results) * 100 if test_results else 0,
            "test_results": test_results
        }
        
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print(f"文件: {filename}")
        print(f"文件中总请求数: {summary['total_requests_in_file']:,}")
        print(f"测试请求数: {summary['tested_requests']}")
        print(f"成功: {summary['successful_tests']}")
        print(f"失败: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        
        if successful:
            print("\n✅ 成功的测试:")
            for test in successful:
                print(f"  - {test['description']}")
                if 'project_count' in test:
                    print(f"    项目数: {test['project_count']}, 总数: {test.get('total_count', 'N/A')}")
        
        if failed:
            print("\n❌ 失败的测试:")
            for test in failed:
                print(f"  - {test['description']}")
                print(f"    错误: {test.get('error', 'Unknown')}")
        
        return summary

    def generate_usage_guide(self, test_results: Dict):
        """生成使用指南"""
        print("\n" + "=" * 60)
        print("📖 使用指南")
        print("=" * 60)
        
        if test_results.get("success_rate", 0) > 0:
            print("🎉 测试成功！您可以开始使用API参数组合了。")
            print()
            print("📋 开始步骤:")
            print("1. 选择要处理的文件块 (overseas_chunk_*.json 或 domestic_chunk_*.json)")
            print("2. 加载JSON文件中的API请求")
            print("3. 为每个请求添加有效的认证token")
            print("4. 发送请求并处理响应")
            print("5. 控制请求频率，避免被限流")
            print()
            print("💡 示例代码:")
            print("""
import json
import requests
import time

# 1. 加载文件
with open('all_api_combinations/overseas_chunk_0001.json', 'r', encoding='utf-8') as f:
    api_requests = json.load(f)

# 2. 处理每个请求
for api_request in api_requests:
    # 添加认证token (如果需要)
    # api_request['headers']['Authorization'] = 'Bearer YOUR_TOKEN'
    
    # 更新时间戳
    api_request['body']['timestamp'] = int(time.time() * 1000)
    
    # 发送请求
    response = requests.post(
        api_request['endpoint'],
        headers=api_request['headers'],
        json=api_request['body']
    )
    
    # 处理响应
    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            projects = data['data']['projectList']
            print(f"获取到 {len(projects)} 个项目")
            # 处理项目数据...
    
    # 控制频率
    time.sleep(0.5)
            """)
        else:
            print("⚠️ 测试失败，可能需要:")
            print("1. 检查网络连接")
            print("2. 确认API端点是否可访问")
            print("3. 检查是否需要认证token")
            print("4. 验证请求参数格式")

def main():
    """主函数"""
    tester = APIChunkTester()
    
    # 测试第一个海外项目文件
    overseas_file = "all_api_combinations/overseas_chunk_0001.json"
    print("🌍 测试海外项目文件")
    overseas_results = tester.test_chunk_sample(overseas_file, sample_size=3)
    
    # 保存测试结果
    with open("first_chunk_test_results.json", 'w', encoding='utf-8') as f:
        json.dump(overseas_results, f, ensure_ascii=False, indent=2)
    
    # 生成使用指南
    tester.generate_usage_guide(overseas_results)
    
    print(f"\n📁 详细测试结果已保存到: first_chunk_test_results.json")

if __name__ == "__main__":
    main()
