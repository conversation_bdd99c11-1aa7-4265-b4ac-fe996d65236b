#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中国项目文件，寻找有数据的参数组合
"""

import json
import requests
import time
from typing import Dict, List

def test_domestic_requests():
    """测试中国项目请求"""
    print("🏠 测试中国项目文件")
    print("=" * 60)
    
    # 加载第一个中国项目文件
    filename = "all_api_combinations/domestic_chunk_0001.json"
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            requests_list = json.load(f)
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        return
    
    print(f"📁 文件包含 {len(requests_list)} 个请求")
    print("🔍 测试前10个请求，寻找有数据的组合...")
    print()
    
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    })
    
    successful_requests = []
    
    for i, api_request in enumerate(requests_list[:10]):
        print(f"--- 测试 {i+1}/10 ---")
        print(f"🧪 {api_request.get('description', 'N/A')}")
        
        # 显示关键参数
        params = api_request["body"]["param"]
        key_params = {}
        for key, value in params.items():
            if value is not None and value != "" and key not in ["pageNo", "pageSize", "keyword", "siteId", "platformId"]:
                key_params[key] = value
        print(f"🔧 参数: {json.dumps(key_params, ensure_ascii=False)}")
        
        try:
            # 更新时间戳
            api_request["body"]["timestamp"] = int(time.time() * 1000)
            
            # 发送请求
            response = session.post(
                api_request["endpoint"],
                headers=api_request["headers"],
                json=api_request["body"],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data:
                    project_list = data["data"].get("projectList", [])
                    total_count = data["data"].get("page", {}).get("totalCount", 0)
                    
                    print(f"✅ 成功 - 项目数: {len(project_list)}, 总数: {total_count}")
                    
                    if len(project_list) > 0:
                        # 显示第一个项目信息
                        first_project = project_list[0]
                        print(f"📝 示例项目: {first_project.get('name', 'N/A')}")
                        print(f"🏢 公司: {first_project.get('companyName', 'N/A')}")
                        print(f"🏭 行业: {first_project.get('industry', 'N/A')}")
                        
                        successful_requests.append({
                            "request": api_request,
                            "project_count": len(project_list),
                            "total_count": total_count,
                            "sample_project": first_project
                        })
                    
                else:
                    print(f"❌ API错误: {data}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        print()
        time.sleep(1)  # 控制频率
    
    # 汇总结果
    print("=" * 60)
    print("📊 测试结果汇总")
    print(f"有数据的请求: {len(successful_requests)}/10")
    
    if successful_requests:
        print("\n✅ 成功获取数据的参数组合:")
        for i, result in enumerate(successful_requests, 1):
            print(f"{i}. {result['request']['description']}")
            print(f"   项目数: {result['project_count']}, 总数: {result['total_count']}")
            print(f"   示例: {result['sample_project'].get('name', 'N/A')}")
        
        # 保存成功的请求
        with open("successful_requests.json", 'w', encoding='utf-8') as f:
            json.dump(successful_requests, f, ensure_ascii=False, indent=2)
        print(f"\n📁 成功的请求已保存到: successful_requests.json")
    else:
        print("\n⚠️ 没有找到有数据的参数组合")
        print("💡 建议:")
        print("1. 尝试其他参数组合")
        print("2. 检查是否需要认证token")
        print("3. 尝试更宽泛的筛选条件")

def test_broader_parameters():
    """测试更宽泛的参数组合"""
    print("\n🔍 测试更宽泛的参数组合")
    print("=" * 60)
    
    # 创建一些更宽泛的测试请求
    base_request = {
        "endpoint": "https://gateway.36kr.com/api/pms/project/list",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Origin": "https://pitchhub.36kr.com",
            "Referer": "https://pitchhub.36kr.com/",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
        },
        "body": {
            "partner_id": "web",
            "timestamp": int(time.time() * 1000),
            "partner_version": "1.0.0",
            "param": {
                "pageNo": "1",
                "pageSize": "20",
                "sort": None,
                "financingRoundIdList": None,
                "ifOverseas": None,
                "labelIdList": None,
                "krPublish": None,
                "isFinancingProject": None,
                "tradeIdList": None,
                "establishYearList": None,
                "provinceIdList": None,
                "keyword": "",
                "siteId": 1,
                "platformId": 2
            }
        }
    }
    
    # 测试不同的参数组合
    test_cases = [
        {
            "description": "只筛选行业: 消费电商",
            "params": {"tradeIdList": ["2"]}
        },
        {
            "description": "只筛选地区: 中国",
            "params": {"ifOverseas": "0"}
        },
        {
            "description": "只筛选省份: 福建省",
            "params": {"provinceIdList": ["2"]}
        },
        {
            "description": "行业+地区: 消费电商+中国",
            "params": {"tradeIdList": ["2"], "ifOverseas": "0"}
        },
        {
            "description": "无任何筛选条件",
            "params": {}
        }
    ]
    
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    })
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- 测试 {i}/{len(test_cases)} ---")
        print(f"🧪 {test_case['description']}")
        
        # 创建请求
        request = json.loads(json.dumps(base_request))
        request["body"]["timestamp"] = int(time.time() * 1000)
        
        # 设置参数
        for key, value in test_case["params"].items():
            request["body"]["param"][key] = value
        
        print(f"🔧 参数: {json.dumps(test_case['params'], ensure_ascii=False)}")
        
        try:
            response = session.post(
                request["endpoint"],
                headers=request["headers"],
                json=request["body"],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data:
                    project_list = data["data"].get("projectList", [])
                    total_count = data["data"].get("page", {}).get("totalCount", 0)
                    
                    print(f"✅ 成功 - 项目数: {len(project_list)}, 总数: {total_count}")
                    
                    if len(project_list) > 0:
                        first_project = project_list[0]
                        print(f"📝 示例项目: {first_project.get('name', 'N/A')}")
                        print(f"🏢 公司: {first_project.get('companyName', 'N/A')}")
                else:
                    print(f"❌ API错误: {data}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        print()
        time.sleep(1)

def main():
    """主函数"""
    # 测试中国项目文件
    test_domestic_requests()
    
    # 测试更宽泛的参数
    test_broader_parameters()

if __name__ == "__main__":
    main()
