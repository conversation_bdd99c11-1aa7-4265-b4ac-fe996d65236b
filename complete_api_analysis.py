#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的36KR API参数组合分析和生成工具
基于API参数组合分析指导文档
"""

import json
import time
import itertools
import os
from typing import Dict, List, Iterator

class CompleteAPIAnalyzer:
    def __init__(self, config_file: str = "parameter-config.json"):
        """初始化分析器"""
        self.config = self.load_config(config_file)
        self.api_template = {
            "endpoint": "https://gateway.36kr.com/api/pms/project/list",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer {token}",
                "Origin": "https://pitchhub.36kr.com",
                "Referer": "https://pitchhub.36kr.com/"
            },
            "body": {
                "partner_id": "web",
                "timestamp": 0,
                "partner_version": "1.0.0",
                "param": {
                    "pageNo": "1",
                    "pageSize": "20",
                    "sort": None,
                    "financingRoundIdList": None,
                    "ifOverseas": None,
                    "labelIdList": None,
                    "krPublish": None,
                    "isFinancingProject": None,
                    "tradeIdList": None,
                    "establishYearList": None,
                    "provinceIdList": None,
                    "keyword": "",
                    "siteId": 1,
                    "platformId": 2
                }
            }
        }

    def load_config(self, filename: str) -> Dict:
        """加载配置文件"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)

    def analyze_parameters(self) -> Dict:
        """分析参数配置"""
        analysis = {
            "parameter_summary": {},
            "dependency_analysis": {},
            "combination_calculations": {}
        }
        
        # 参数摘要
        for key, values in self.config.items():
            if key not in ['projectList', 'page'] and isinstance(values, list):
                analysis["parameter_summary"][key] = {
                    "count": len(values),
                    "codes": [item["code"] for item in values],
                    "names": [item["name"] for item in values]
                }
        
        # 依赖关系分析
        analysis["dependency_analysis"] = {
            "ifOverseas_provinceIdList_relationship": {
                "description": "ifOverseas与provinceIdList存在互斥关系",
                "rules": {
                    "overseas_projects": "ifOverseas=1时，provinceIdList不适用",
                    "domestic_projects": "ifOverseas=0时，provinceIdList有34个省份选项"
                }
            }
        }
        
        # 组合计算
        trade_count = len(self.config["tradeIdList"])
        financing_count = len(self.config["financingRoundIdList"])
        year_count = len(self.config["establishYearList"])
        kr_publish_count = len(self.config["krPublish"])
        financing_project_count = len(self.config["isFinancingProject"])
        label_count = len(self.config["labelIdList"])
        province_count = len(self.config["provinceIdList"])
        
        # 海外项目组合
        overseas_combinations = (
            trade_count * financing_count * year_count * 
            kr_publish_count * financing_project_count * label_count
        )
        
        # 中国项目组合
        domestic_combinations = (
            trade_count * financing_count * year_count * province_count *
            kr_publish_count * financing_project_count * label_count
        )
        
        total_combinations = overseas_combinations + domestic_combinations
        
        analysis["combination_calculations"] = {
            "overseas_combinations": {
                "count": overseas_combinations,
                "formula": f"{trade_count} × {financing_count} × {year_count} × {kr_publish_count} × {financing_project_count} × {label_count}",
                "description": "海外项目组合数（不包含省份参数）"
            },
            "domestic_combinations": {
                "count": domestic_combinations,
                "formula": f"{trade_count} × {financing_count} × {year_count} × {province_count} × {kr_publish_count} × {financing_project_count} × {label_count}",
                "description": "中国项目组合数（包含省份参数）"
            },
            "total_combinations": {
                "count": total_combinations,
                "formula": f"{overseas_combinations} + {domestic_combinations}",
                "description": "总组合数"
            }
        }
        
        return analysis

    def generate_curl_commands(self, requests: List[Dict], output_file: str = "all_api_curl_commands.txt"):
        """生成所有curl命令"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("36KR API 完整curl命令集合\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"总计: {len(requests)} 个API请求\n\n")
            
            for i, request in enumerate(requests, 1):
                # 更新时间戳
                request["body"]["timestamp"] = int(time.time() * 1000)
                
                f.write(f"# {i}. {request['description']}\n")
                f.write(f"# 类别: {request.get('category', 'unknown')}\n")
                
                # 生成curl命令
                headers = []
                for key, value in request["headers"].items():
                    if key == "Authorization":
                        headers.append(f'-H "{key}: Bearer YOUR_TOKEN_HERE"')
                    else:
                        headers.append(f'-H "{key}: {value}"')
                
                body_json = json.dumps(request["body"], ensure_ascii=False, separators=(',', ':'))
                
                curl_cmd = f"""curl -X {request["method"]} \\
  '{request["endpoint"]}' \\
  {' '.join(headers)} \\
  -d '{body_json}'"""
                
                f.write(curl_cmd + "\n\n")
        
        print(f"所有curl命令已保存到: {output_file}")

    def generate_sample_requests(self, overseas_samples: int = 10, domestic_samples: int = 10) -> List[Dict]:
        """生成示例请求"""
        requests = []
        
        # 海外项目示例
        overseas_count = 0
        for combo in itertools.product(
            self.config["tradeIdList"][:3],  # 前3个行业
            self.config["financingRoundIdList"][:3],  # 前3个融资轮次
            self.config["establishYearList"][:2],  # 前2个年份
            self.config["krPublish"],
            self.config["isFinancingProject"],
            self.config["labelIdList"][:2]  # 前2个标签
        ):
            if overseas_count >= overseas_samples:
                break
                
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            
            request["body"]["param"]["tradeIdList"] = [str(combo[0]["code"])]
            request["body"]["param"]["financingRoundIdList"] = [str(combo[1]["code"])]
            request["body"]["param"]["establishYearList"] = [str(combo[2]["code"])]
            request["body"]["param"]["ifOverseas"] = "1"
            request["body"]["param"]["provinceIdList"] = None
            request["body"]["param"]["krPublish"] = str(combo[3]["code"])
            request["body"]["param"]["isFinancingProject"] = str(combo[4]["code"])
            request["body"]["param"]["labelIdList"] = [str(combo[5]["code"])]
            
            desc_parts = [item["name"] for item in combo]
            request["description"] = f"海外项目: {' + '.join(desc_parts)}"
            request["category"] = "overseas"
            
            requests.append(request)
            overseas_count += 1
        
        # 中国项目示例
        domestic_count = 0
        for combo in itertools.product(
            self.config["tradeIdList"][:2],  # 前2个行业
            self.config["financingRoundIdList"][:2],  # 前2个融资轮次
            self.config["establishYearList"][:2],  # 前2个年份
            self.config["provinceIdList"][:3],  # 前3个省份
            self.config["krPublish"],
            self.config["isFinancingProject"],
            self.config["labelIdList"][:2]  # 前2个标签
        ):
            if domestic_count >= domestic_samples:
                break
                
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            
            request["body"]["param"]["tradeIdList"] = [str(combo[0]["code"])]
            request["body"]["param"]["financingRoundIdList"] = [str(combo[1]["code"])]
            request["body"]["param"]["establishYearList"] = [str(combo[2]["code"])]
            request["body"]["param"]["ifOverseas"] = "0"
            request["body"]["param"]["provinceIdList"] = [str(combo[3]["code"])]
            request["body"]["param"]["krPublish"] = str(combo[4]["code"])
            request["body"]["param"]["isFinancingProject"] = str(combo[5]["code"])
            request["body"]["param"]["labelIdList"] = [str(combo[6]["code"])]
            
            desc_parts = [item["name"] for item in combo]
            request["description"] = f"中国项目: {' + '.join(desc_parts)}"
            request["category"] = "domestic"
            
            requests.append(request)
            domestic_count += 1
        
        return requests

    def save_complete_analysis(self, output_dir: str = "complete_analysis"):
        """保存完整分析结果"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 1. 保存参数分析
        analysis = self.analyze_parameters()
        analysis_file = os.path.join(output_dir, "parameter_analysis.json")
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        # 2. 生成示例请求
        sample_requests = self.generate_sample_requests(overseas_samples=20, domestic_samples=30)
        samples_file = os.path.join(output_dir, "sample_requests.json")
        with open(samples_file, 'w', encoding='utf-8') as f:
            json.dump(sample_requests, f, ensure_ascii=False, indent=2)
        
        # 3. 生成curl命令
        curl_file = os.path.join(output_dir, "sample_curl_commands.txt")
        self.generate_curl_commands(sample_requests, curl_file)
        
        # 4. 生成总结报告
        self.generate_summary_report(analysis, sample_requests, output_dir)
        
        print(f"完整分析结果已保存到: {output_dir}")
        print(f"- 参数分析: {analysis_file}")
        print(f"- 示例请求: {samples_file}")
        print(f"- Curl命令: {curl_file}")
        print(f"- 总结报告: {os.path.join(output_dir, 'COMPLETE_ANALYSIS_REPORT.md')}")

    def generate_summary_report(self, analysis: Dict, sample_requests: List[Dict], output_dir: str):
        """生成总结报告"""
        report_file = os.path.join(output_dir, "COMPLETE_ANALYSIS_REPORT.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 36KR API完整参数组合分析报告\n\n")
            
            f.write("## 📊 参数配置分析\n\n")
            f.write("### 参数统计\n")
            f.write("| 参数名 | 数量 | 说明 |\n")
            f.write("|--------|------|------|\n")
            
            param_names = {
                "tradeIdList": "行业分类",
                "financingRoundIdList": "融资轮次", 
                "establishYearList": "成立年份",
                "ifOverseas": "地域选择",
                "provinceIdList": "省份选择",
                "krPublish": "36氪发布状态",
                "isFinancingProject": "融资项目状态",
                "labelIdList": "项目标签"
            }
            
            for param, info in analysis["parameter_summary"].items():
                name = param_names.get(param, param)
                f.write(f"| {name} | {info['count']}个 | {param} |\n")
            
            f.write("\n### 🔗 参数依赖关系\n\n")
            f.write("**关键发现**: ifOverseas与provinceIdList存在互斥关系\n\n")
            f.write("- **海外项目** (ifOverseas=1): provinceIdList参数不适用\n")
            f.write("- **中国项目** (ifOverseas=0): provinceIdList参数有34个省份选项\n\n")
            
            f.write("## 🧮 组合数量计算\n\n")
            calc = analysis["combination_calculations"]
            
            f.write("### 海外项目组合\n")
            f.write(f"- **数量**: {calc['overseas_combinations']['count']:,}\n")
            f.write(f"- **公式**: {calc['overseas_combinations']['formula']}\n")
            f.write(f"- **说明**: {calc['overseas_combinations']['description']}\n\n")
            
            f.write("### 中国项目组合\n")
            f.write(f"- **数量**: {calc['domestic_combinations']['count']:,}\n")
            f.write(f"- **公式**: {calc['domestic_combinations']['formula']}\n")
            f.write(f"- **说明**: {calc['domestic_combinations']['description']}\n\n")
            
            f.write("### 总组合数\n")
            f.write(f"- **总计**: {calc['total_combinations']['count']:,}\n")
            f.write(f"- **公式**: {calc['total_combinations']['formula']}\n\n")
            
            f.write("## 🚀 API请求示例\n\n")
            f.write(f"生成了 {len(sample_requests)} 个示例请求:\n")
            
            overseas_count = len([r for r in sample_requests if r.get('category') == 'overseas'])
            domestic_count = len([r for r in sample_requests if r.get('category') == 'domestic'])
            
            f.write(f"- 海外项目示例: {overseas_count} 个\n")
            f.write(f"- 中国项目示例: {domestic_count} 个\n\n")
            
            f.write("### 示例请求格式\n\n")
            if sample_requests:
                example = sample_requests[0]
                f.write("```json\n")
                f.write(json.dumps(example, ensure_ascii=False, indent=2))
                f.write("\n```\n\n")
            
            f.write("## 📋 使用说明\n\n")
            f.write("1. **查看参数分析**: `parameter_analysis.json`\n")
            f.write("2. **使用示例请求**: `sample_requests.json`\n")
            f.write("3. **执行curl命令**: `sample_curl_commands.txt`\n")
            f.write("4. **理解依赖关系**: 注意ifOverseas与provinceIdList的互斥关系\n\n")
            
            f.write("## ⚠️ 重要提醒\n\n")
            f.write("- 总组合数量巨大，实际使用时建议采样\n")
            f.write("- 所有请求都需要有效的Bearer token\n")
            f.write("- 建议控制请求频率，避免被限流\n")

def main():
    """主函数"""
    print("36KR API完整参数组合分析工具")
    print("=" * 50)
    
    analyzer = CompleteAPIAnalyzer()
    
    # 执行完整分析
    analyzer.save_complete_analysis()
    
    # 显示关键统计信息
    analysis = analyzer.analyze_parameters()
    calc = analysis["combination_calculations"]
    
    print(f"\n📊 关键统计:")
    print(f"海外项目组合数: {calc['overseas_combinations']['count']:,}")
    print(f"中国项目组合数: {calc['domestic_combinations']['count']:,}")
    print(f"总组合数: {calc['total_combinations']['count']:,}")

if __name__ == "__main__":
    main()
