#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
36KR API参数组合使用指南
完整的开始使用教程和示例代码
"""

import json
import requests
import time
import os
from typing import Dict, List, Generator
import pandas as pd

class APIDataCollector:
    def __init__(self, base_dir: str = "all_api_combinations"):
        """初始化数据收集器"""
        self.base_dir = base_dir
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        })
        
        # 加载统计信息
        self.stats = self.load_statistics()
        
    def load_statistics(self) -> Dict:
        """加载生成统计信息"""
        stats_file = os.path.join(self.base_dir, "generation_statistics.json")
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def get_available_files(self) -> Dict[str, List[str]]:
        """获取可用的文件列表"""
        if not os.path.exists(self.base_dir):
            return {"overseas": [], "domestic": []}
        
        files = os.listdir(self.base_dir)
        overseas_files = sorted([f for f in files if f.startswith("overseas_chunk_") and f.endswith(".json")])
        domestic_files = sorted([f for f in files if f.startswith("domestic_chunk_") and f.endswith(".json")])
        
        return {
            "overseas": overseas_files,
            "domestic": domestic_files
        }
    
    def load_chunk(self, filename: str) -> List[Dict]:
        """加载单个chunk文件"""
        filepath = os.path.join(self.base_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载文件失败 {filename}: {e}")
            return []
    
    def process_single_request(self, api_request: Dict, add_token: str = None) -> Dict:
        """处理单个API请求"""
        try:
            # 更新时间戳
            api_request["body"]["timestamp"] = int(time.time() * 1000)
            
            # 添加token（如果提供）
            if add_token:
                api_request["headers"]["Authorization"] = f"Bearer {add_token}"
            
            # 发送请求
            response = self.session.post(
                api_request["endpoint"],
                headers=api_request["headers"],
                json=api_request["body"],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data:
                    project_list = data["data"].get("projectList", [])
                    page_info = data["data"].get("page", {})
                    
                    return {
                        "success": True,
                        "projects": project_list,
                        "total_count": page_info.get("totalCount", 0),
                        "page_info": page_info,
                        "combination_id": api_request.get("combination_id", ""),
                        "description": api_request.get("description", ""),
                        "category": api_request.get("category", "")
                    }
                else:
                    return {
                        "success": False,
                        "error": f"API返回错误: {data}",
                        "combination_id": api_request.get("combination_id", "")
                    }
            else:
                return {
                    "success": False,
                    "error": f"HTTP错误: {response.status_code}",
                    "combination_id": api_request.get("combination_id", "")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"请求异常: {str(e)}",
                "combination_id": api_request.get("combination_id", "")
            }
    
    def batch_process_chunk(self, filename: str, max_requests: int = 10, delay: float = 0.5, token: str = None) -> List[Dict]:
        """批量处理chunk文件"""
        print(f"🚀 开始处理文件: {filename}")
        
        # 加载文件
        requests_list = self.load_chunk(filename)
        if not requests_list:
            return []
        
        print(f"📁 文件包含 {len(requests_list)} 个请求")
        print(f"🔄 将处理前 {min(max_requests, len(requests_list))} 个请求")
        
        results = []
        successful_count = 0
        
        for i, api_request in enumerate(requests_list[:max_requests]):
            print(f"📤 处理请求 {i+1}/{min(max_requests, len(requests_list))}: {api_request.get('combination_id', 'N/A')}")
            
            result = self.process_single_request(api_request, token)
            results.append(result)
            
            if result["success"]:
                successful_count += 1
                print(f"✅ 成功 - 获取到 {len(result['projects'])} 个项目 (总计: {result['total_count']})")
            else:
                print(f"❌ 失败 - {result['error']}")
            
            # 控制请求频率
            if i < min(max_requests, len(requests_list)) - 1:
                time.sleep(delay)
        
        print(f"\n📊 处理完成: {successful_count}/{len(results)} 成功")
        return results
    
    def save_results_to_csv(self, results: List[Dict], output_file: str = "api_results.csv"):
        """保存结果到CSV文件"""
        # 提取项目数据
        all_projects = []
        
        for result in results:
            if result["success"] and result["projects"]:
                for project in result["projects"]:
                    project_data = {
                        "combination_id": result["combination_id"],
                        "category": result["category"],
                        "description": result["description"],
                        "project_id": project.get("id", ""),
                        "project_name": project.get("name", ""),
                        "company_name": project.get("companyName", ""),
                        "industry": project.get("industry", ""),
                        "financing_round": project.get("financingRound", ""),
                        "establish_year": project.get("establishYear", ""),
                        "province": project.get("province", ""),
                        "city": project.get("city", ""),
                        "description_text": project.get("description", "")
                    }
                    all_projects.append(project_data)
        
        if all_projects:
            df = pd.DataFrame(all_projects)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"📊 已保存 {len(all_projects)} 个项目数据到: {output_file}")
        else:
            print("⚠️ 没有项目数据可保存")

def show_getting_started_guide():
    """显示开始使用指南"""
    print("🚀 36KR API参数组合使用指南")
    print("=" * 60)
    
    collector = APIDataCollector()
    
    # 显示统计信息
    if collector.stats:
        print("📊 数据概览:")
        print(f"  总组合数: {collector.stats.get('total_combinations', 0):,}")
        print(f"  海外项目组合: {collector.stats.get('overseas_combinations', 0):,}")
        print(f"  中国项目组合: {collector.stats.get('domestic_combinations', 0):,}")
        print(f"  总文件数: {collector.stats.get('total_chunks', 0)}")
        print()
    
    # 显示可用文件
    files = collector.get_available_files()
    print("📁 可用文件:")
    print(f"  海外项目文件: {len(files['overseas'])} 个")
    print(f"  中国项目文件: {len(files['domestic'])} 个")
    print()
    
    # 显示前几个文件名
    if files['overseas']:
        print("🌍 海外项目文件示例:")
        for i, filename in enumerate(files['overseas'][:3]):
            print(f"  {i+1}. {filename}")
        if len(files['overseas']) > 3:
            print(f"  ... 还有 {len(files['overseas']) - 3} 个文件")
        print()
    
    if files['domestic']:
        print("🏠 中国项目文件示例:")
        for i, filename in enumerate(files['domestic'][:3]):
            print(f"  {i+1}. {filename}")
        if len(files['domestic']) > 3:
            print(f"  ... 还有 {len(files['domestic']) - 3} 个文件")
        print()

def demo_basic_usage():
    """演示基本使用方法"""
    print("💡 基本使用演示")
    print("=" * 60)
    
    collector = APIDataCollector()
    files = collector.get_available_files()
    
    if files['overseas']:
        # 演示处理第一个海外文件
        first_file = files['overseas'][0]
        print(f"📝 演示处理文件: {first_file}")
        
        # 处理前5个请求
        results = collector.batch_process_chunk(first_file, max_requests=5, delay=1.0)
        
        # 保存结果
        if results:
            collector.save_results_to_csv(results, "demo_results.csv")
        
        print("\n✅ 演示完成!")
    else:
        print("❌ 没有找到可用的文件")

def show_advanced_usage_examples():
    """显示高级使用示例"""
    print("\n🔧 高级使用示例")
    print("=" * 60)
    
    print("""
# 1. 批量处理多个文件
collector = APIDataCollector()
files = collector.get_available_files()

all_results = []
for filename in files['overseas'][:5]:  # 处理前5个海外文件
    results = collector.batch_process_chunk(filename, max_requests=100)
    all_results.extend(results)

# 2. 添加认证token
results = collector.batch_process_chunk(
    'overseas_chunk_0001.json', 
    max_requests=50,
    token='YOUR_API_TOKEN_HERE'
)

# 3. 自定义处理逻辑
def custom_process():
    collector = APIDataCollector()
    chunk_data = collector.load_chunk('overseas_chunk_0001.json')
    
    for api_request in chunk_data[:10]:
        result = collector.process_single_request(api_request)
        if result['success']:
            # 自定义数据处理
            for project in result['projects']:
                print(f"项目: {project.get('name', 'N/A')}")
                print(f"公司: {project.get('companyName', 'N/A')}")
                print("-" * 30)

# 4. 数据过滤和分析
def analyze_results(results):
    successful_results = [r for r in results if r['success']]
    total_projects = sum(len(r['projects']) for r in successful_results)
    
    print(f"成功请求: {len(successful_results)}")
    print(f"总项目数: {total_projects}")
    
    # 按类别统计
    overseas_count = len([r for r in successful_results if r['category'] == 'overseas'])
    domestic_count = len([r for r in successful_results if r['category'] == 'domestic'])
    
    print(f"海外项目请求: {overseas_count}")
    print(f"中国项目请求: {domestic_count}")
    """)

def main():
    """主函数"""
    print("🎯 36KR API参数组合 - 完整使用指南")
    print("=" * 60)
    
    # 1. 显示开始指南
    show_getting_started_guide()
    
    # 2. 演示基本使用
    demo_basic_usage()
    
    # 3. 显示高级示例
    show_advanced_usage_examples()
    
    print("\n📋 快速开始步骤:")
    print("1. 运行此脚本查看可用文件")
    print("2. 选择要处理的文件类型 (海外/中国)")
    print("3. 使用 batch_process_chunk() 方法处理文件")
    print("4. 保存结果到CSV文件进行分析")
    print("5. 根据需要调整请求频率和批次大小")
    
    print("\n⚠️ 注意事项:")
    print("- 控制请求频率，避免被API限流")
    print("- 如果需要认证，请添加有效的token")
    print("- 建议从小批次开始测试")
    print("- 监控API响应，及时处理错误")

if __name__ == "__main__":
    main()
