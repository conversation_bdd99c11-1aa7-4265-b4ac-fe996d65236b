[{"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530084104"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 专精特新小巨人", "category": "overseas", "combination_id": "overseas_215001"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530018562"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 专精特新", "category": "overseas", "combination_id": "overseas_215002"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2076252066686472"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 创新型中小企业", "category": "overseas", "combination_id": "overseas_215003"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530280704"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 高新技术企业", "category": "overseas", "combination_id": "overseas_215004"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530297091"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 科技型中小企业", "category": "overseas", "combination_id": "overseas_215005"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530198787"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 独角兽", "category": "overseas", "combination_id": "overseas_215006"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530215175"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 瞪羚企业", "category": "overseas", "combination_id": "overseas_215007"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530247942"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2011年 + 否 + 否 + 雏鹰企业", "category": "overseas", "combination_id": "overseas_215008"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530084104"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 专精特新小巨人", "category": "overseas", "combination_id": "overseas_215009"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530018562"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 专精特新", "category": "overseas", "combination_id": "overseas_215010"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2076252066686472"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 创新型中小企业", "category": "overseas", "combination_id": "overseas_215011"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530280704"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 高新技术企业", "category": "overseas", "combination_id": "overseas_215012"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530297091"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 科技型中小企业", "category": "overseas", "combination_id": "overseas_215013"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530198787"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 独角兽", "category": "overseas", "combination_id": "overseas_215014"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530215175"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 瞪羚企业", "category": "overseas", "combination_id": "overseas_215015"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530247942"], "krPublish": "1", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 是 + 雏鹰企业", "category": "overseas", "combination_id": "overseas_215016"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530084104"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 专精特新小巨人", "category": "overseas", "combination_id": "overseas_215017"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530018562"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 专精特新", "category": "overseas", "combination_id": "overseas_215018"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2076252066686472"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 创新型中小企业", "category": "overseas", "combination_id": "overseas_215019"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530280704"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 高新技术企业", "category": "overseas", "combination_id": "overseas_215020"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530297091"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 科技型中小企业", "category": "overseas", "combination_id": "overseas_215021"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530198787"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 独角兽", "category": "overseas", "combination_id": "overseas_215022"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530215175"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 瞪羚企业", "category": "overseas", "combination_id": "overseas_215023"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530247942"], "krPublish": "1", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 是 + 否 + 雏鹰企业", "category": "overseas", "combination_id": "overseas_215024"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530084104"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 专精特新小巨人", "category": "overseas", "combination_id": "overseas_215025"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530018562"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 专精特新", "category": "overseas", "combination_id": "overseas_215026"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2076252066686472"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 创新型中小企业", "category": "overseas", "combination_id": "overseas_215027"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530280704"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 高新技术企业", "category": "overseas", "combination_id": "overseas_215028"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530297091"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 科技型中小企业", "category": "overseas", "combination_id": "overseas_215029"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530198787"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 独角兽", "category": "overseas", "combination_id": "overseas_215030"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530215175"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 瞪羚企业", "category": "overseas", "combination_id": "overseas_215031"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530247942"], "krPublish": "0", "isFinancingProject": "1", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 是 + 雏鹰企业", "category": "overseas", "combination_id": "overseas_215032"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530084104"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 专精特新小巨人", "category": "overseas", "combination_id": "overseas_215033"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626026, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530018562"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 专精特新", "category": "overseas", "combination_id": "overseas_215034"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626027, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2076252066686472"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 创新型中小企业", "category": "overseas", "combination_id": "overseas_215035"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626027, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530280704"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 高新技术企业", "category": "overseas", "combination_id": "overseas_215036"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626027, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530297091"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 科技型中小企业", "category": "overseas", "combination_id": "overseas_215037"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626027, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530198787"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 独角兽", "category": "overseas", "combination_id": "overseas_215038"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626027, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530215175"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 瞪羚企业", "category": "overseas", "combination_id": "overseas_215039"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/", "Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"}, "body": {"partner_id": "web", "timestamp": 1756200626027, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": "1", "labelIdList": ["2074768530247942"], "krPublish": "0", "isFinancingProject": "0", "tradeIdList": ["999"], "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "海外项目: 其他 + 其他 + 2010年及以前 + 否 + 否 + 雏鹰企业", "category": "overseas", "combination_id": "overseas_215040"}]