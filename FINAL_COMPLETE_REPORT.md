# 36KR API参数组合完整分析报告

## 🎯 任务完成总结

✅ **已根据API参数组合分析指导文档完成所有工作**

基于您提供的`API参数组合分析指导.md`文档，我重新分析了参数依赖关系，并生成了正确的API请求组合。

## 📊 关键发现

### 参数依赖关系修正
根据指导文档发现的**重要依赖关系**：
- **ifOverseas = 1** (海外)：`provinceIdList` 参数不适用
- **ifOverseas = 0** (中国)：`provinceIdList` 参数有34个省份选项

这个依赖关系显著影响了总组合数的计算！

### 实际参数配置
经过分析`parameter-config.json`，实际包含**8个参数**（不是指导文档中提到的9个）：

| 参数名 | 数量 | 实际code值 |
|--------|------|-----------|
| tradeIdList | 15个 | 2,3,7,8,9,10,11,12,13,15,16,23,25,27,999 |
| financingRoundIdList | 28个 | 1-30 |
| establishYearList | 16个 | 2025-2010 |
| ifOverseas | 2个 | 0,1 |
| provinceIdList | 34个 | 2-359 |
| krPublish | 2个 | 0,1 |
| isFinancingProject | 2个 | 0,1 |
| labelIdList | 8个 | 大数字ID |

**注意**：parameter-config.json中没有`sort`字段，与指导文档不同。

## 🧮 正确的组合数量计算

### 基于依赖关系的分情况计算

#### 情况1：海外项目 (ifOverseas = 1)
```
15 × 28 × 16 × 2 × 2 × 8 = 215,040
```

#### 情况2：中国项目 (ifOverseas = 0)  
```
15 × 28 × 16 × 34 × 2 × 2 × 8 = 7,311,360
```

#### 总组合数
```
215,040 + 7,311,360 = 7,526,400
```

**这比之前简单乘法计算的43,868,160减少了很多！**

## 🚀 生成的API请求

### 完整生成结果
1. **高级API请求** (advanced_api_requests/)
   - 海外项目组合: 10,000个 (10个文件块)
   - 中国项目组合: 10,000个 (10个文件块)
   - 总计: 20,000个实际API请求

2. **完整分析** (complete_analysis/)
   - 参数分析: parameter_analysis.json
   - 示例请求: 50个 (sample_requests.json)
   - Curl命令: 50个 (sample_curl_commands.txt)
   - 分析报告: COMPLETE_ANALYSIS_REPORT.md

## 📋 API请求格式示例

### 海外项目请求
```bash
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "partner_id":"web",
    "timestamp":1756199598440,
    "partner_version":"1.0.0",
    "param":{
      "pageNo":"1",
      "pageSize":"20",
      "financingRoundIdList":["1"],
      "ifOverseas":"1",
      "labelIdList":["2074768530084104"],
      "krPublish":"1",
      "isFinancingProject":"1",
      "tradeIdList":["2"],
      "establishYearList":["2025"],
      "provinceIdList":null,
      "keyword":"",
      "siteId":1,
      "platformId":2
    }
  }'
```

### 中国项目请求
```bash
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "partner_id":"web",
    "timestamp":1756199598440,
    "partner_version":"1.0.0",
    "param":{
      "pageNo":"1",
      "pageSize":"20",
      "financingRoundIdList":["1"],
      "ifOverseas":"0",
      "labelIdList":["2074768530084104"],
      "krPublish":"1",
      "isFinancingProject":"1",
      "tradeIdList":["2"],
      "establishYearList":["2025"],
      "provinceIdList":["2"],
      "keyword":"",
      "siteId":1,
      "platformId":2
    }
  }'
```

## 📁 生成的文件结构

```
d:\Research\start-up\36kr-v5\
├── advanced_api_requests/
│   ├── overseas_combinations_chunk_001.json (1000个海外请求)
│   ├── overseas_combinations_chunk_002.json
│   ├── ...
│   ├── overseas_combinations_chunk_010.json
│   ├── domestic_combinations_chunk_001.json (1000个中国请求)
│   ├── domestic_combinations_chunk_002.json
│   ├── ...
│   ├── domestic_combinations_chunk_010.json
│   └── advanced_statistics.json
├── complete_analysis/
│   ├── parameter_analysis.json
│   ├── sample_requests.json (50个示例)
│   ├── sample_curl_commands.txt (50个curl命令)
│   └── COMPLETE_ANALYSIS_REPORT.md
├── correct_api_requests/ (之前生成的210个请求)
├── api_requests/ (最初生成的281个请求)
└── 各种分析脚本和工具
```

## 🛠️ 可用工具脚本

1. **advanced_api_generator.py** - 基于依赖关系的高级生成器
2. **complete_api_analysis.py** - 完整分析工具
3. **correct_api_generator.py** - 修正版生成器
4. **generate_api_urls.py** - URL和curl命令生成器

## 📈 对比分析

| 计算方法 | 组合数量 | 说明 |
|---------|---------|------|
| 简单乘法 | 43,868,160 | 忽略参数依赖关系 |
| **依赖关系计算** | **7,526,400** | **考虑ifOverseas与provinceIdList互斥** |
| 实际生成 | 20,000 | 采样生成的实际请求数 |

## ✅ 验证结果

### 参数依赖关系验证
- ✅ 海外项目 (ifOverseas=1) 时，provinceIdList 设为 null
- ✅ 中国项目 (ifOverseas=0) 时，provinceIdList 包含具体省份code
- ✅ 所有参数使用正确的code值（如tradeIdList从2开始，不是1）

### API请求格式验证
- ✅ 端点: https://gateway.36kr.com/api/pms/project/list
- ✅ 方法: POST
- ✅ 请求体结构正确
- ✅ 时间戳自动更新
- ✅ 所有必需的header包含

## 🎉 最终总结

**成功完成了基于API参数组合分析指导的完整任务**：

1. ✅ **正确识别了参数依赖关系** - ifOverseas与provinceIdList的互斥关系
2. ✅ **修正了组合数量计算** - 从4387万降到753万
3. ✅ **生成了20,000个实际API请求** - 分为海外和中国两类
4. ✅ **提供了完整的分析工具** - 包含参数分析、示例请求、curl命令
5. ✅ **使用了正确的参数code值** - 基于实际的parameter-config.json

**理论总组合数**: 7,526,400  
**实际生成请求数**: 20,000  
**覆盖率**: 0.27%

所有生成的API请求都考虑了参数依赖关系，可以直接用于数据采集和分析！
