{"projectList": [{"id": 1897800775228678, "logoUrl": "https://img.36krcdn.com/hsossms/20250813/v2_b8cb3910e8e74fcba95b00955ed0ea5f@000000_oswg149435oswg3544oswg3544_img_png?x-oss-process=image/resize,m_mfit,w_100,h_100", "name": "会享健康科技", "briefIntro": "致力于打造自由职业者社区平台。", "industry": "社交网络  区块链", "lastestFinancingRound": "未融资", "area": "浙江省", "establishTime": "2022年", "hasBp": 0}, {"id": 2540276451899267, "logoUrl": "https://img.36krcdn.com/hsossms/20231130/v2_a5ef3c681f084c3d810419081650bbf0@000000_oswg38108oswg301oswg299_img_png?x-oss-process=image/resize,m_mfit,w_100,h_100", "name": "智言Copilot", "briefIntro": "数据报告Ai,为10亿用户节约90%的报告时间", "industry": "前沿技术", "lastestFinancingRound": "未融资", "area": "上海市", "establishTime": "2017年", "hasBp": 0}], "page": {"prePage": 1, "nextPage": 2, "pageNo": 1, "pageSize": 20, "totalPage": 50, "totalCount": 44665}, "tradeIdList": [{"code": 2, "name": "消费电商"}, {"code": 3, "name": "汽车出行"}, {"code": 7, "name": "产业升级"}, {"code": 8, "name": "前沿技术"}, {"code": 9, "name": "医疗健康"}, {"code": 10, "name": "先进制造"}, {"code": 11, "name": "通信/半导体"}, {"code": 12, "name": "物联网/硬件"}, {"code": 13, "name": "工具软件"}, {"code": 15, "name": "农林牧渔"}, {"code": 16, "name": "能源环保"}, {"code": 17, "name": "本地生活"}, {"code": 18, "name": "体育游戏"}, {"code": 19, "name": "跨境出海"}, {"code": 20, "name": "房产地产"}, {"code": 21, "name": "旅游"}, {"code": 22, "name": "广告营销"}, {"code": 23, "name": "智能硬件"}, {"code": 24, "name": "物流"}, {"code": 25, "name": "区块链"}, {"code": 26, "name": "传统制造"}, {"code": 27, "name": "元宇宙"}, {"code": 999, "name": "其他"}], "financingRoundIdList": [{"code": 1, "name": "未融资"}, {"code": 2, "name": "种子轮"}, {"code": 3, "name": "天使轮"}, {"code": 4, "name": "Pre-A轮"}, {"code": 5, "name": "A轮"}, {"code": 6, "name": "A+轮"}, {"code": 7, "name": "Pre-B轮"}, {"code": 8, "name": "B轮"}, {"code": 9, "name": "B+轮"}, {"code": 10, "name": "C轮"}, {"code": 11, "name": "C+轮"}, {"code": 12, "name": "D轮"}, {"code": 13, "name": "D+轮"}, {"code": 14, "name": "E轮"}, {"code": 16, "name": "F轮"}, {"code": 18, "name": "G轮"}, {"code": 19, "name": "H轮"}, {"code": 24, "name": "股权融资"}, {"code": 21, "name": "战略融资"}, {"code": 26, "name": "定向增发"}, {"code": 20, "name": "Pre-IPO"}, {"code": 27, "name": "基石轮"}, {"code": 25, "name": "已上市"}, {"code": 23, "name": "IPO"}, {"code": 28, "name": "新三板"}, {"code": 29, "name": "已退市/私有化"}, {"code": 30, "name": "并购/合并"}, {"code": 999, "name": "其他"}], "establishYearList": [{"code": 2025, "name": "2025年"}, {"code": 2024, "name": "2024年"}, {"code": 2023, "name": "2023年"}, {"code": 2022, "name": "2022年"}, {"code": 2021, "name": "2021年"}, {"code": 2020, "name": "2020年"}, {"code": 2019, "name": "2019年"}, {"code": 2018, "name": "2018年"}, {"code": 2017, "name": "2017年"}, {"code": 2016, "name": "2016年"}, {"code": 2015, "name": "2015年"}, {"code": 2014, "name": "2014年"}, {"code": 2013, "name": "2013年"}, {"code": 2012, "name": "2012年"}, {"code": 2011, "name": "2011年"}, {"code": 2010, "name": "2010年及以前"}], "ifOverseas": [{"code": 0, "name": "中国"}, {"code": 1, "name": "海外"}], "provinceIdList": [{"code": 2, "name": "福建省"}, {"code": 3, "name": "广东省"}, {"code": 5, "name": "北京市"}, {"code": 6, "name": "香港特别行政区"}, {"code": 7, "name": "吉林省"}, {"code": 9, "name": "天津市"}, {"code": 10, "name": "辽宁省"}, {"code": 13, "name": "上海市"}, {"code": 14, "name": "河北省"}, {"code": 16, "name": "江苏省"}, {"code": 18, "name": "内蒙古自治区"}, {"code": 31, "name": "台湾省"}, {"code": 38, "name": "贵州省"}, {"code": 46, "name": "宁夏回族自治区"}, {"code": 52, "name": "浙江省"}, {"code": 56, "name": "安徽省"}, {"code": 59, "name": "山东省"}, {"code": 63, "name": "黑龙江省"}, {"code": 77, "name": "山西省"}, {"code": 86, "name": "陕西省"}, {"code": 112, "name": "广西壮族自治区"}, {"code": 115, "name": "河南省"}, {"code": 152, "name": "重庆市"}, {"code": 156, "name": "四川省"}, {"code": 159, "name": "云南省"}, {"code": 174, "name": "澳门特别行政区"}, {"code": 175, "name": "湖北省"}, {"code": 192, "name": "西藏自治区"}, {"code": 232, "name": "甘肃省"}, {"code": 247, "name": "海南省"}, {"code": 287, "name": "新疆维吾尔自治区"}, {"code": 301, "name": "青海省"}, {"code": 321, "name": "湖南省"}, {"code": 359, "name": "江西省"}], "sort": [{"code": 3, "name": "项目推荐"}, {"code": 1, "name": "最近更新"}, {"code": 2, "name": "最新收录"}], "krPublish": [{"code": 1, "name": "是"}, {"code": 0, "name": "否"}], "isFinancingProject": [{"code": 1, "name": "是"}, {"code": 0, "name": "否"}], "labelIdList": [{"code": 2074768530084104, "name": "专精特新小巨人"}, {"code": 2074768530018562, "name": "专精特新"}, {"code": 2076252066686472, "name": "创新型中小企业"}, {"code": 2074768530280704, "name": "高新技术企业"}, {"code": 2074768530297091, "name": "科技型中小企业"}, {"code": 2074768530198787, "name": "独角兽"}, {"code": 2074768530215175, "name": "瞪羚企业"}, {"code": 2074768530247942, "name": "雏鹰企业"}]}