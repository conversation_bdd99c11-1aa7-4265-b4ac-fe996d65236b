{"parameter_summary": {"tradeIdList": {"count": 15, "codes": [2, 3, 7, 8, 9, 10, 11, 12, 13, 15, 16, 23, 25, 27, 999], "names": ["消费电商", "汽车出行", "产业升级", "前沿技术", "医疗健康", "先进制造", "通信/半导体", "物联网/硬件", "工具软件", "农林牧渔", "能源环保", "智能硬件", "区块链", "元宇宙", "其他"]}, "financingRoundIdList": {"count": 28, "codes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 18, 19, 24, 21, 26, 20, 27, 25, 23, 28, 29, 30, 999], "names": ["未融资", "种子轮", "天使轮", "Pre-A轮", "A轮", "A+轮", "Pre-B轮", "B轮", "B+轮", "C轮", "C+轮", "D轮", "D+轮", "E轮", "F轮", "G轮", "H轮", "股权融资", "战略融资", "定向增发", "Pre-IPO", "基石轮", "已上市", "IPO", "新三板", "已退市/私有化", "并购/合并", "其他"]}, "establishYearList": {"count": 16, "codes": [2025, 2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014, 2013, 2012, 2011, 2010], "names": ["2025年", "2024年", "2023年", "2022年", "2021年", "2020年", "2019年", "2018年", "2017年", "2016年", "2015年", "2014年", "2013年", "2012年", "2011年", "2010年及以前"]}, "ifOverseas": {"count": 2, "codes": [0, 1], "names": ["中国", "海外"]}, "provinceIdList": {"count": 34, "codes": [2, 3, 5, 6, 7, 9, 10, 13, 14, 16, 18, 31, 38, 46, 52, 56, 59, 63, 77, 86, 112, 115, 152, 156, 159, 174, 175, 192, 232, 247, 287, 301, 321, 359], "names": ["福建省", "广东省", "北京市", "香港特别行政区", "吉林省", "天津市", "辽宁省", "上海市", "河北省", "江苏省", "内蒙古自治区", "台湾省", "贵州省", "宁夏回族自治区", "浙江省", "安徽省", "山东省", "黑龙江省", "山西省", "陕西省", "广西壮族自治区", "河南省", "重庆市", "四川省", "云南省", "澳门特别行政区", "湖北省", "西藏自治区", "甘肃省", "海南省", "新疆维吾尔自治区", "青海省", "湖南省", "江西省"]}, "krPublish": {"count": 2, "codes": [1, 0], "names": ["是", "否"]}, "isFinancingProject": {"count": 2, "codes": [1, 0], "names": ["是", "否"]}, "labelIdList": {"count": 8, "codes": [2074768530084104, 2074768530018562, 2076252066686472, 2074768530280704, 2074768530297091, 2074768530198787, 2074768530215175, 2074768530247942], "names": ["专精特新小巨人", "专精特新", "创新型中小企业", "高新技术企业", "科技型中小企业", "独角兽", "瞪羚企业", "雏鹰企业"]}}, "dependency_analysis": {"ifOverseas_provinceIdList_relationship": {"description": "ifOverseas与provinceIdList存在互斥关系", "rules": {"overseas_projects": "ifOverseas=1时，provinceIdList不适用", "domestic_projects": "ifOverseas=0时，provinceIdList有34个省份选项"}}}, "combination_calculations": {"overseas_combinations": {"count": 215040, "formula": "15 × 28 × 16 × 2 × 2 × 8", "description": "海外项目组合数（不包含省份参数）"}, "domestic_combinations": {"count": 7311360, "formula": "15 × 28 × 16 × 34 × 2 × 2 × 8", "description": "中国项目组合数（包含省份参数）"}, "total_combinations": {"count": 7526400, "formula": "215040 + 7311360", "description": "总组合数"}}}