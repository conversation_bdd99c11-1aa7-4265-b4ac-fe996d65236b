#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
36KR企业数据大规模采集器
支持分页处理、进度跟踪、断点续传、自动重试
"""

import json
import requests
import time
import os
import sqlite3
import threading
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import signal
import sys

@dataclass
class CrawlProgress:
    """爬取进度数据类"""
    combination_id: str
    category: str
    description: str
    total_count: int
    total_pages: int
    completed_pages: int
    status: str  # 'pending', 'crawling', 'completed', 'failed', 'skipped'
    last_update: str
    error_message: Optional[str] = None

class EnterpriseDataCrawler:
    def __init__(self, base_dir: str = "all_api_combinations", output_dir: str = "crawl_results"):
        """初始化爬虫"""
        self.base_dir = base_dir
        self.output_dir = output_dir
        self.db_path = os.path.join(output_dir, "crawl_progress.db")
        self.data_dir = os.path.join(output_dir, "data")
        self.reports_dir = os.path.join(output_dir, "reports")
        
        # 创建目录
        for dir_path in [output_dir, self.data_dir, self.reports_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 初始化数据库
        self.init_database()
        
        # 配置日志
        self.setup_logging()
        
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Origin": "https://pitchhub.36kr.com",
            "Referer": "https://pitchhub.36kr.com/",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
        })
        
        # 控制变量
        self.stop_crawling = False
        self.pause_crawling = False
        
        # 统计信息
        self.stats = {
            "total_combinations": 0,
            "completed_combinations": 0,
            "skipped_combinations": 0,
            "failed_combinations": 0,
            "total_enterprises": 0,
            "high_count_combinations": 0  # totalCount > 1000
        }
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建进度表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS crawl_progress (
                combination_id TEXT PRIMARY KEY,
                category TEXT,
                description TEXT,
                total_count INTEGER,
                total_pages INTEGER,
                completed_pages INTEGER DEFAULT 0,
                status TEXT DEFAULT 'pending',
                last_update TEXT,
                error_message TEXT,
                file_path TEXT
            )
        ''')
        
        # 创建企业数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enterprise_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                combination_id TEXT,
                page_no INTEGER,
                project_id TEXT,
                project_name TEXT,
                company_name TEXT,
                industry TEXT,
                financing_round TEXT,
                establish_year TEXT,
                province TEXT,
                city TEXT,
                description TEXT,
                crawl_time TEXT,
                UNIQUE(combination_id, page_no, project_id)
            )
        ''')
        
        conn.commit()
        conn.close()

    def setup_logging(self):
        """设置日志"""
        log_file = os.path.join(self.output_dir, "crawler.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"接收到信号 {signum}，准备停止爬虫...")
        self.stop_crawling = True

    def load_api_combinations(self) -> List[Dict]:
        """加载所有API组合"""
        all_combinations = []
        
        # 加载海外项目
        for i in range(1, 45):  # 44个海外文件
            filename = f"overseas_chunk_{i:04d}.json"
            filepath = os.path.join(self.base_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    combinations = json.load(f)
                    all_combinations.extend(combinations)
        
        # 加载中国项目
        for i in range(1, 1464):  # 1463个中国文件
            filename = f"domestic_chunk_{i:04d}.json"
            filepath = os.path.join(self.base_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    combinations = json.load(f)
                    all_combinations.extend(combinations)
        
        self.logger.info(f"加载了 {len(all_combinations)} 个API组合")
        return all_combinations

    def get_total_count_for_combination(self, api_request: Dict) -> Tuple[int, int, str]:
        """获取单个组合的总数和页数"""
        try:
            # 更新时间戳
            api_request["body"]["timestamp"] = int(time.time() * 1000)
            api_request["body"]["param"]["pageNo"] = "1"
            
            response = self.session.post(
                api_request["endpoint"],
                headers=api_request["headers"],
                json=api_request["body"],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data:
                    page_info = data["data"].get("page", {})
                    total_count = page_info.get("totalCount", 0)
                    total_pages = page_info.get("totalPage", 0)
                    
                    return total_count, total_pages, "success"
                else:
                    return 0, 0, f"API错误: {data}"
            else:
                return 0, 0, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            return 0, 0, f"请求异常: {str(e)}"

    def scan_all_combinations(self, max_workers: int = 3, batch_size: int = 1000):
        """扫描所有组合，获取总数信息 - 支持并发和断点续传"""
        self.logger.info(f"开始扫描所有API组合 (并发数: {max_workers}, 批次大小: {batch_size})...")

        combinations = self.load_api_combinations()
        self.stats["total_combinations"] = len(combinations)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 检查已扫描的组合
        cursor.execute("SELECT combination_id FROM crawl_progress")
        scanned_ids = {row[0] for row in cursor.fetchall()}

        # 获取待扫描的组合
        pending_combinations = []
        for i, combination in enumerate(combinations):
            combination_id = combination.get("combination_id", f"unknown_{i}")
            if combination_id not in scanned_ids:
                pending_combinations.append((i, combination))

        conn.close()

        if not pending_combinations:
            self.logger.info("所有组合已扫描完成")
            return

        self.logger.info(f"找到 {len(pending_combinations)} 个待扫描组合")

        # 使用线程池进行并发扫描
        from concurrent.futures import ThreadPoolExecutor, as_completed

        scan_count = 0
        high_count_combinations = []

        # 分批处理
        for batch_start in range(0, len(pending_combinations), batch_size):
            if self.stop_crawling:
                break

            batch_end = min(batch_start + batch_size, len(pending_combinations))
            batch = pending_combinations[batch_start:batch_end]

            self.logger.info(f"处理批次 {batch_start//batch_size + 1}: {len(batch)} 个组合")

            # 并发扫描当前批次
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交扫描任务
                future_to_combo = {
                    executor.submit(self.scan_single_combination, combo_data): combo_data
                    for combo_data in batch
                }

                batch_results = []

                for future in as_completed(future_to_combo):
                    if self.stop_crawling:
                        break

                    # 暂停检查
                    while self.pause_crawling and not self.stop_crawling:
                        time.sleep(1)

                    combo_data = future_to_combo[future]
                    try:
                        result = future.result()
                        if result:
                            batch_results.append(result)
                            scan_count += 1

                            if result["total_count"] > 1000:
                                high_count_combinations.append(result)
                                self.stats["high_count_combinations"] += 1

                            if result["total_count"] == 0:
                                self.stats["skipped_combinations"] += 1

                            # 显示进度
                            if scan_count % 50 == 0:
                                progress_percent = (scan_count / len(pending_combinations)) * 100
                                self.logger.info(f"扫描进度: {scan_count}/{len(pending_combinations)} ({progress_percent:.1f}%)")

                    except Exception as e:
                        self.logger.error(f"扫描组合失败: {str(e)}")

            # 批量保存结果
            if batch_results:
                self.save_scan_results(batch_results)
                self.logger.info(f"批次完成，已保存 {len(batch_results)} 个结果")

            # 批次间暂停
            if not self.stop_crawling:
                time.sleep(1)

        # 生成扫描报告
        self.generate_scan_report(high_count_combinations)

        self.logger.info(f"扫描完成！总计: {scan_count} 个组合")

    def scan_single_combination(self, combo_data: tuple) -> Dict:
        """扫描单个组合"""
        i, combination = combo_data
        combination_id = combination.get("combination_id", f"unknown_{i}")

        try:
            total_count, total_pages, status = self.get_total_count_for_combination(combination)

            return {
                "combination_id": combination_id,
                "category": combination.get("category", "unknown"),
                "description": combination.get("description", ""),
                "total_count": total_count,
                "total_pages": total_pages,
                "status": "pending" if total_count > 0 else "skipped",
                "error_message": None if status == "success" else status,
                "scan_time": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"扫描组合 {combination_id} 失败: {str(e)}")
            return {
                "combination_id": combination_id,
                "category": combination.get("category", "unknown"),
                "description": combination.get("description", ""),
                "total_count": 0,
                "total_pages": 0,
                "status": "failed",
                "error_message": str(e),
                "scan_time": datetime.now().isoformat()
            }

    def save_scan_results(self, results: List[Dict]):
        """批量保存扫描结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for result in results:
            cursor.execute('''
                INSERT OR REPLACE INTO crawl_progress
                (combination_id, category, description, total_count, total_pages,
                 completed_pages, status, last_update, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                result["combination_id"], result["category"], result["description"],
                result["total_count"], result["total_pages"], 0,
                result["status"], result["scan_time"], result.get("error_message")
            ))

        conn.commit()
        conn.close()

    def generate_scan_report(self, high_count_combinations: List[Dict]):
        """生成扫描报告"""
        report_file = os.path.join(self.reports_dir, f"scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 统计信息
        cursor.execute("SELECT status, COUNT(*) FROM crawl_progress GROUP BY status")
        status_stats = dict(cursor.fetchall())
        
        cursor.execute("SELECT COUNT(*) FROM crawl_progress WHERE total_count > 0")
        valid_combinations = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(total_count) FROM crawl_progress WHERE total_count > 0")
        total_enterprises = cursor.fetchone()[0] or 0
        
        report = {
            "scan_time": datetime.now().isoformat(),
            "statistics": {
                "total_combinations": self.stats["total_combinations"],
                "valid_combinations": valid_combinations,
                "skipped_combinations": status_stats.get("skipped", 0),
                "total_enterprises": total_enterprises,
                "high_count_combinations": len(high_count_combinations)
            },
            "high_count_combinations": high_count_combinations[:50],  # 前50个
            "status_distribution": status_stats
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        conn.close()
        
        self.logger.info(f"扫描报告已保存: {report_file}")
        
        # 打印摘要
        print("\n" + "="*60)
        print("📊 扫描结果摘要")
        print("="*60)
        print(f"总组合数: {self.stats['total_combinations']:,}")
        print(f"有效组合: {valid_combinations:,}")
        print(f"跳过组合: {status_stats.get('skipped', 0):,}")
        print(f"预计企业总数: {total_enterprises:,}")
        print(f"高数量组合 (>1000): {len(high_count_combinations)}")
        
        if high_count_combinations:
            print(f"\n🔥 前10个高数量组合:")
            for i, combo in enumerate(high_count_combinations[:10], 1):
                print(f"{i:2d}. {combo['description'][:50]}... ({combo['total_count']:,} 企业)")

    def crawl_combination_data(self, combination_id: str, max_pages: int = None) -> bool:
        """爬取单个组合的所有数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取组合信息
        cursor.execute('''
            SELECT category, description, total_count, total_pages, completed_pages 
            FROM crawl_progress WHERE combination_id = ?
        ''', (combination_id,))
        
        result = cursor.fetchone()
        if not result:
            conn.close()
            return False
        
        category, description, total_count, total_pages, completed_pages = result
        
        if total_count == 0:
            conn.close()
            return True  # 跳过无数据的组合
        
        # 限制最大页数
        if max_pages:
            total_pages = min(total_pages, max_pages)
        
        self.logger.info(f"开始爬取: {combination_id} ({completed_pages+1}/{total_pages} 页)")
        
        # 更新状态为爬取中
        cursor.execute('''
            UPDATE crawl_progress SET status = 'crawling', last_update = ?
            WHERE combination_id = ?
        ''', (datetime.now().isoformat(), combination_id))
        conn.commit()
        
        success = True
        
        try:
            for page_no in range(completed_pages + 1, total_pages + 1):
                if self.stop_crawling:
                    break
                
                # 暂停检查
                while self.pause_crawling and not self.stop_crawling:
                    time.sleep(1)
                
                # 爬取单页数据
                page_success = self.crawl_single_page(combination_id, page_no)
                
                if page_success:
                    # 更新进度
                    cursor.execute('''
                        UPDATE crawl_progress SET completed_pages = ?, last_update = ?
                        WHERE combination_id = ?
                    ''', (page_no, datetime.now().isoformat(), combination_id))
                    conn.commit()
                else:
                    success = False
                    break
                
                # 控制请求频率
                time.sleep(0.3)
        
        except Exception as e:
            self.logger.error(f"爬取组合 {combination_id} 时出错: {str(e)}")
            success = False
        
        # 更新最终状态
        final_status = "completed" if success and not self.stop_crawling else "failed"
        cursor.execute('''
            UPDATE crawl_progress SET status = ?, last_update = ?
            WHERE combination_id = ?
        ''', (final_status, datetime.now().isoformat(), combination_id))
        conn.commit()
        conn.close()
        
        return success

    def crawl_single_page(self, combination_id: str, page_no: int) -> bool:
        """爬取单页数据"""
        try:
            # 从原始组合中获取API请求参数
            api_request = self.get_api_request_by_id(combination_id)
            if not api_request:
                self.logger.error(f"无法找到组合 {combination_id} 的API请求参数")
                return False

            # 设置页码
            api_request["body"]["param"]["pageNo"] = str(page_no)
            api_request["body"]["timestamp"] = int(time.time() * 1000)

            # 发送请求
            response = self.session.post(
                api_request["endpoint"],
                headers=api_request["headers"],
                json=api_request["body"],
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data:
                    projects = data["data"].get("projectList", [])

                    # 保存企业数据
                    self.save_enterprise_data(combination_id, page_no, projects)

                    self.logger.info(f"页面 {page_no} 爬取成功: {len(projects)} 个企业")
                    return True
                else:
                    self.logger.error(f"API返回错误: {data}")
                    return False
            else:
                self.logger.error(f"HTTP错误 {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"爬取页面 {page_no} 失败: {str(e)}")
            return False

    def get_api_request_by_id(self, combination_id: str) -> Optional[Dict]:
        """根据组合ID获取API请求参数"""
        # 从缓存中获取，如果没有则从文件中查找
        if not hasattr(self, '_api_cache'):
            self._api_cache = {}

        if combination_id in self._api_cache:
            return self._api_cache[combination_id]

        # 从文件中查找
        combinations = self.load_api_combinations()
        for combo in combinations:
            if combo.get("combination_id") == combination_id:
                self._api_cache[combination_id] = combo
                return combo

        return None

    def save_enterprise_data(self, combination_id: str, page_no: int, projects: List[Dict]):
        """保存企业数据到数据库"""
        if not projects:
            return

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        crawl_time = datetime.now().isoformat()

        for project in projects:
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO enterprise_data
                    (combination_id, page_no, project_id, project_name, company_name,
                     industry, financing_round, establish_year, province, city,
                     description, crawl_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    combination_id,
                    page_no,
                    project.get("id", ""),
                    project.get("name", ""),
                    project.get("companyName", ""),
                    project.get("industry", ""),
                    project.get("financingRound", ""),
                    project.get("establishYear", ""),
                    project.get("province", ""),
                    project.get("city", ""),
                    project.get("description", ""),
                    crawl_time
                ))
            except Exception as e:
                self.logger.error(f"保存企业数据失败: {str(e)}")

        conn.commit()
        conn.close()

        self.stats["total_enterprises"] += len(projects)

    def get_scan_progress(self) -> Dict:
        """获取扫描进度"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 总组合数
        total_combinations = self.stats.get("total_combinations", 7526400)

        # 已扫描数
        cursor.execute('SELECT COUNT(*) FROM crawl_progress')
        scanned_count = cursor.fetchone()[0]

        # 扫描状态分布
        cursor.execute('SELECT status, COUNT(*) FROM crawl_progress GROUP BY status')
        status_distribution = dict(cursor.fetchall())

        # 高数量组合
        cursor.execute('SELECT COUNT(*) FROM crawl_progress WHERE total_count > 1000')
        high_count_combinations = cursor.fetchone()[0]

        # 总企业数
        cursor.execute('SELECT SUM(total_count) FROM crawl_progress WHERE total_count > 0')
        total_enterprises = cursor.fetchone()[0] or 0

        conn.close()

        scan_progress = {
            "total_combinations": total_combinations,
            "scanned_combinations": scanned_count,
            "remaining_combinations": total_combinations - scanned_count,
            "scan_progress_percent": (scanned_count / total_combinations * 100) if total_combinations > 0 else 0,
            "status_distribution": status_distribution,
            "high_count_combinations": high_count_combinations,
            "total_enterprises_found": total_enterprises
        }

        return scan_progress

    def get_crawl_status(self) -> Dict:
        """获取爬取状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 基本状态统计
        cursor.execute('''
            SELECT status, COUNT(*), SUM(total_count), SUM(completed_pages * 20)
            FROM crawl_progress GROUP BY status
        ''')

        status_info = {}
        for status, count, total_enterprises, crawled_enterprises in cursor.fetchall():
            status_info[status] = {
                "count": count,
                "total_enterprises": total_enterprises or 0,
                "crawled_enterprises": crawled_enterprises or 0
            }

        # 总体进度
        cursor.execute('SELECT COUNT(*) FROM enterprise_data')
        actual_enterprises = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM crawl_progress WHERE total_count > 1000')
        high_count_total = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM crawl_progress WHERE total_count > 1000 AND status = "completed"')
        high_count_completed = cursor.fetchone()[0]

        status_info["summary"] = {
            "actual_enterprises_crawled": actual_enterprises,
            "high_count_combinations": {
                "total": high_count_total,
                "completed": high_count_completed
            }
        }

        conn.close()
        return status_info

    def export_data_to_csv(self, output_file: str = None) -> str:
        """导出数据到CSV文件"""
        if not output_file:
            output_file = os.path.join(self.reports_dir, f"enterprise_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")

        conn = sqlite3.connect(self.db_path)

        # 使用pandas导出
        try:
            import pandas as pd

            query = '''
                SELECT
                    e.combination_id,
                    p.category,
                    p.description as combination_description,
                    e.project_id,
                    e.project_name,
                    e.company_name,
                    e.industry,
                    e.financing_round,
                    e.establish_year,
                    e.province,
                    e.city,
                    e.description as project_description,
                    e.crawl_time
                FROM enterprise_data e
                LEFT JOIN crawl_progress p ON e.combination_id = p.combination_id
                ORDER BY e.combination_id, e.page_no
            '''

            df = pd.read_sql_query(query, conn)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')

            self.logger.info(f"数据已导出到: {output_file}")
            self.logger.info(f"导出企业数量: {len(df)}")

        except ImportError:
            # 如果没有pandas，使用csv模块
            import csv

            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    e.combination_id, p.category, p.description,
                    e.project_id, e.project_name, e.company_name,
                    e.industry, e.financing_round, e.establish_year,
                    e.province, e.city, e.description, e.crawl_time
                FROM enterprise_data e
                LEFT JOIN crawl_progress p ON e.combination_id = p.combination_id
                ORDER BY e.combination_id, e.page_no
            ''')

            with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow([
                    'combination_id', 'category', 'combination_description',
                    'project_id', 'project_name', 'company_name',
                    'industry', 'financing_round', 'establish_year',
                    'province', 'city', 'project_description', 'crawl_time'
                ])
                writer.writerows(cursor.fetchall())

        conn.close()
        return output_file

    def generate_progress_report(self) -> str:
        """生成进度报告"""
        report_file = os.path.join(self.reports_dir, f"progress_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

        status = self.get_crawl_status()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取高数量组合详情
        cursor.execute('''
            SELECT combination_id, description, total_count, total_pages,
                   completed_pages, status
            FROM crawl_progress
            WHERE total_count > 1000
            ORDER BY total_count DESC
        ''')

        high_count_details = []
        for row in cursor.fetchall():
            high_count_details.append({
                "combination_id": row[0],
                "description": row[1],
                "total_count": row[2],
                "total_pages": row[3],
                "completed_pages": row[4],
                "status": row[5],
                "progress_percent": (row[4] / row[3] * 100) if row[3] > 0 else 0
            })

        # 获取失败的组合
        cursor.execute('''
            SELECT combination_id, description, error_message
            FROM crawl_progress
            WHERE status = 'failed'
            ORDER BY last_update DESC
            LIMIT 20
        ''')

        failed_combinations = []
        for row in cursor.fetchall():
            failed_combinations.append({
                "combination_id": row[0],
                "description": row[1],
                "error_message": row[2]
            })

        report = {
            "report_time": datetime.now().isoformat(),
            "status_summary": status,
            "high_count_combinations": high_count_details,
            "failed_combinations": failed_combinations,
            "recommendations": self.generate_recommendations(status)
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        conn.close()

        self.logger.info(f"进度报告已生成: {report_file}")
        return report_file

    def generate_recommendations(self, status: Dict) -> List[str]:
        """生成建议"""
        recommendations = []

        if "failed" in status and status["failed"]["count"] > 0:
            recommendations.append(f"有 {status['failed']['count']} 个组合爬取失败，建议检查错误日志并重试")

        if "pending" in status and status["pending"]["count"] > 0:
            recommendations.append(f"还有 {status['pending']['count']} 个组合待爬取")

        high_count_info = status.get("summary", {}).get("high_count_combinations", {})
        if high_count_info.get("total", 0) > 0:
            completed_ratio = high_count_info.get("completed", 0) / high_count_info.get("total", 1)
            if completed_ratio < 0.5:
                recommendations.append("高数量组合(>1000企业)完成度较低，建议优先处理")

        return recommendations

    def resume_crawling(self, max_workers: int = 3):
        """恢复爬取"""
        self.logger.info("开始/恢复数据爬取...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取待爬取的组合
        cursor.execute('''
            SELECT combination_id FROM crawl_progress 
            WHERE status IN ('pending', 'failed') AND total_count > 0
            ORDER BY total_count DESC
        ''')
        
        pending_combinations = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if not pending_combinations:
            self.logger.info("没有待爬取的组合")
            return
        
        self.logger.info(f"找到 {len(pending_combinations)} 个待爬取组合")
        
        # 使用线程池爬取
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(self.crawl_combination_data, combo_id): combo_id 
                for combo_id in pending_combinations
            }
            
            for future in as_completed(futures):
                if self.stop_crawling:
                    break
                
                combo_id = futures[future]
                try:
                    success = future.result()
                    if success:
                        self.stats["completed_combinations"] += 1
                    else:
                        self.stats["failed_combinations"] += 1
                except Exception as e:
                    self.logger.error(f"爬取组合 {combo_id} 失败: {str(e)}")
                    self.stats["failed_combinations"] += 1

def main():
    """主函数"""
    crawler = EnterpriseDataCrawler()

    print("🚀 36KR企业数据大规模采集器")
    print("="*60)
    print("1. 📊 扫描所有组合 (获取总数信息) - 支持并发和断点续传")
    print("2. 🔄 开始/恢复爬取")
    print("3. 📈 查看详细状态")
    print("4. 📋 生成进度报告")
    print("5. 💾 导出数据到CSV")
    print("6. ⏸️  暂停/恢复操作")
    print("7. 🔍 查看高数量组合")
    print("8. ❌ 查看失败组合")
    print("9. 📊 查看扫描进度")
    print("0. 🚪 退出")

    while True:
        choice = input("\n请选择操作 (0-9): ").strip()

        if choice == "1":
            print("\n📊 配置扫描参数:")
            max_workers = input("请输入扫描并发数 (默认3, 建议1-5): ").strip()
            max_workers = int(max_workers) if max_workers.isdigit() and 1 <= int(max_workers) <= 10 else 3

            batch_size = input("请输入批次大小 (默认1000): ").strip()
            batch_size = int(batch_size) if batch_size.isdigit() and int(batch_size) > 0 else 1000

            print(f"\n开始扫描所有组合 (并发: {max_workers}, 批次: {batch_size})...")
            print("💡 提示: 可随时按 Ctrl+C 暂停，选择菜单项6恢复")
            crawler.scan_all_combinations(max_workers=max_workers, batch_size=batch_size)

        elif choice == "2":
            max_workers = input("请输入并发线程数 (默认3): ").strip()
            max_workers = int(max_workers) if max_workers.isdigit() else 3
            print(f"\n开始爬取，使用 {max_workers} 个线程...")
            crawler.resume_crawling(max_workers=max_workers)

        elif choice == "3":
            status = crawler.get_crawl_status()
            print("\n📊 详细状态报告:")
            print("="*50)

            for status_name, info in status.items():
                if status_name == "summary":
                    continue
                print(f"{status_name:12}: {info['count']:6} 组合, {info['total_enterprises']:8} 企业")

            if "summary" in status:
                summary = status["summary"]
                print(f"\n实际爬取企业数: {summary['actual_enterprises_crawled']:,}")
                high_count = summary['high_count_combinations']
                print(f"高数量组合: {high_count['completed']}/{high_count['total']} 完成")

        elif choice == "4":
            print("\n生成进度报告...")
            report_file = crawler.generate_progress_report()
            print(f"✅ 报告已生成: {report_file}")

        elif choice == "5":
            print("\n导出数据到CSV...")
            csv_file = crawler.export_data_to_csv()
            print(f"✅ 数据已导出: {csv_file}")

        elif choice == "6":
            if crawler.pause_crawling:
                crawler.pause_crawling = False
                print("✅ 操作已恢复")
            else:
                crawler.pause_crawling = True
                print("⏸️ 操作已暂停 (扫描和爬取都会暂停)")

        elif choice == "7":
            show_high_count_combinations(crawler)

        elif choice == "8":
            show_failed_combinations(crawler)

        elif choice == "9":
            show_scan_progress(crawler)

        elif choice == "0":
            print("👋 退出程序")
            crawler.stop_crawling = True
            break

        else:
            print("❌ 无效选择，请重新输入")

def show_high_count_combinations(crawler):
    """显示高数量组合"""
    conn = sqlite3.connect(crawler.db_path)
    cursor = conn.cursor()

    cursor.execute('''
        SELECT combination_id, description, total_count, total_pages,
               completed_pages, status
        FROM crawl_progress
        WHERE total_count > 1000
        ORDER BY total_count DESC
        LIMIT 20
    ''')

    print("\n🔥 高数量组合 (>1000企业) - 前20个:")
    print("="*80)
    print(f"{'序号':<4} {'状态':<10} {'企业数':<8} {'进度':<12} {'描述':<40}")
    print("-"*80)

    for i, row in enumerate(cursor.fetchall(), 1):
        combo_id, description, total_count, total_pages, completed_pages, status = row
        progress = f"{completed_pages}/{total_pages}" if total_pages > 0 else "0/0"
        desc_short = description[:35] + "..." if len(description) > 35 else description
        print(f"{i:<4} {status:<10} {total_count:<8} {progress:<12} {desc_short}")

    conn.close()

def show_failed_combinations(crawler):
    """显示失败的组合"""
    conn = sqlite3.connect(crawler.db_path)
    cursor = conn.cursor()

    cursor.execute('''
        SELECT combination_id, description, error_message, last_update
        FROM crawl_progress
        WHERE status = 'failed'
        ORDER BY last_update DESC
        LIMIT 15
    ''')

    print("\n❌ 失败的组合 - 最近15个:")
    print("="*80)

    failed_rows = cursor.fetchall()
    if not failed_rows:
        print("✅ 没有失败的组合")
    else:
        for i, row in enumerate(failed_rows, 1):
            combo_id, description, error_msg, last_update = row
            print(f"{i}. {description[:50]}...")
            print(f"   错误: {error_msg}")
            print(f"   时间: {last_update}")
            print()

    conn.close()

def show_scan_progress(crawler):
    """显示扫描进度"""
    progress = crawler.get_scan_progress()

    print("\n📊 扫描进度报告")
    print("="*80)

    # 基本进度
    total = progress["total_combinations"]
    scanned = progress["scanned_combinations"]
    remaining = progress["remaining_combinations"]
    percent = progress["scan_progress_percent"]

    print(f"总组合数: {total:,}")
    print(f"已扫描: {scanned:,}")
    print(f"剩余: {remaining:,}")
    print(f"进度: {percent:.2f}%")

    # 进度条
    bar_length = 50
    filled_length = int(bar_length * percent / 100)
    bar = "█" * filled_length + "░" * (bar_length - filled_length)
    print(f"[{bar}] {percent:.1f}%")

    # 状态分布
    print(f"\n📋 扫描状态分布:")
    status_dist = progress["status_distribution"]
    for status, count in status_dist.items():
        status_name = {
            "pending": "待爬取",
            "skipped": "无数据",
            "completed": "已完成",
            "failed": "失败",
            "crawling": "爬取中"
        }.get(status, status)
        print(f"  {status_name}: {count:,} 个组合")

    # 重要统计
    print(f"\n🔥 重要发现:")
    print(f"高数量组合 (>1000企业): {progress['high_count_combinations']:,} 个")
    print(f"预计企业总数: {progress['total_enterprises_found']:,}")

    # 预估剩余时间
    if scanned > 0 and remaining > 0:
        # 假设平均扫描速度
        avg_time_per_combo = 0.5  # 秒
        estimated_hours = (remaining * avg_time_per_combo) / 3600
        print(f"\n⏱️ 预估剩余时间: {estimated_hours:.1f} 小时 (按单线程计算)")
        print(f"💡 建议使用3-5个并发线程加速扫描")

if __name__ == "__main__":
    main()
