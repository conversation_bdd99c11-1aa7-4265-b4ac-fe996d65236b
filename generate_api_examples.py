#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成API请求示例和计算总组合数
"""

import json
import math
from typing import Dict, List

def load_parameter_config(filename: str = "parameter-config.json") -> Dict:
    """加载参数配置文件"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def calculate_total_combinations(config: Dict) -> int:
    """计算所有参数的总组合数"""
    # 获取各参数列表的长度
    trade_count = len(config["tradeIdList"])
    financing_count = len(config["financingRoundIdList"])
    establish_count = len(config["establishYearList"])
    overseas_count = len(config["ifOverseas"])
    province_count = len(config["provinceIdList"])
    sort_count = len(config["sort"])
    kr_publish_count = len(config["krPublish"])
    financing_project_count = len(config["isFinancingProject"])
    label_count = len(config["labelIdList"])
    
    # 计算笛卡尔积
    total = (trade_count * financing_count * establish_count * 
             overseas_count * province_count * sort_count * 
             kr_publish_count * financing_project_count * label_count)
    
    return total

def generate_sample_requests(config: Dict, num_samples: int = 10) -> List[Dict]:
    """生成示例API请求"""
    samples = []
    
    # 基础API模板
    base_template = {
        "endpoint": "https://gateway.36kr.com/api/pms/project/list",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer {token}",
            "Origin": "https://pitchhub.36kr.com",
            "Referer": "https://pitchhub.36kr.com/"
        },
        "body": {
            "partner_id": "web",
            "timestamp": 1756198229393,
            "partner_version": "1.0.0",
            "param": {
                "pageNo": "1",
                "pageSize": "20",
                "sort": None,
                "financingRoundIdList": None,
                "ifOverseas": None,
                "labelIdList": None,
                "krPublish": None,
                "isFinancingProject": None,
                "tradeIdList": None,
                "establishYearList": None,
                "provinceIdList": None,
                "keyword": "",
                "siteId": 1,
                "platformId": 2
            }
        }
    }
    
    # 生成示例组合
    examples = [
        # 示例1: 只选择行业
        {
            "tradeIdList": [str(config["tradeIdList"][0]["code"])],
            "description": f"行业筛选: {config['tradeIdList'][0]['name']}"
        },
        # 示例2: 行业 + 融资轮次
        {
            "tradeIdList": [str(config["tradeIdList"][1]["code"])],
            "financingRoundIdList": [str(config["financingRoundIdList"][2]["code"])],
            "description": f"行业+融资轮次: {config['tradeIdList'][1]['name']} + {config['financingRoundIdList'][2]['name']}"
        },
        # 示例3: 地区 + 成立时间
        {
            "provinceIdList": [str(config["provinceIdList"][0]["code"])],
            "establishYearList": [str(config["establishYearList"][0]["code"])],
            "description": f"地区+成立时间: {config['provinceIdList'][0]['name']} + {config['establishYearList'][0]['name']}"
        },
        # 示例4: 多参数组合
        {
            "tradeIdList": [str(config["tradeIdList"][2]["code"])],
            "financingRoundIdList": [str(config["financingRoundIdList"][1]["code"])],
            "provinceIdList": [str(config["provinceIdList"][1]["code"])],
            "sort": str(config["sort"][0]["code"]),
            "krPublish": str(config["krPublish"][0]["code"]),
            "description": f"多参数组合: {config['tradeIdList'][2]['name']} + {config['financingRoundIdList'][1]['name']} + {config['provinceIdList'][1]['name']} + {config['sort'][0]['name']} + {config['krPublish'][0]['name']}"
        },
        # 示例5: 全参数组合
        {
            "tradeIdList": [str(config["tradeIdList"][3]["code"])],
            "financingRoundIdList": [str(config["financingRoundIdList"][3]["code"])],
            "establishYearList": [str(config["establishYearList"][1]["code"])],
            "ifOverseas": str(config["ifOverseas"][0]["code"]),
            "provinceIdList": [str(config["provinceIdList"][2]["code"])],
            "sort": str(config["sort"][1]["code"]),
            "krPublish": str(config["krPublish"][1]["code"]),
            "isFinancingProject": str(config["isFinancingProject"][0]["code"]),
            "labelIdList": [str(config["labelIdList"][0]["code"])],
            "description": f"全参数组合: {config['tradeIdList'][3]['name']} + {config['financingRoundIdList'][3]['name']} + {config['establishYearList'][1]['name']} + {config['ifOverseas'][0]['name']} + {config['provinceIdList'][2]['name']} + {config['sort'][1]['name']} + {config['krPublish'][1]['name']} + {config['isFinancingProject'][0]['name']} + {config['labelIdList'][0]['name']}"
        }
    ]
    
    for example in examples:
        request = json.loads(json.dumps(base_template))
        description = example.pop("description")
        
        # 设置参数
        for key, value in example.items():
            request["body"]["param"][key] = value
        
        request["description"] = description
        samples.append(request)
    
    return samples

def print_parameter_summary(config: Dict):
    """打印参数摘要"""
    print("参数配置摘要:")
    print("=" * 50)
    print(f"行业类别 (tradeIdList): {len(config['tradeIdList'])} 个")
    print(f"融资轮次 (financingRoundIdList): {len(config['financingRoundIdList'])} 个")
    print(f"成立年份 (establishYearList): {len(config['establishYearList'])} 个")
    print(f"地区选择 (ifOverseas): {len(config['ifOverseas'])} 个")
    print(f"省份地区 (provinceIdList): {len(config['provinceIdList'])} 个")
    print(f"排序方式 (sort): {len(config['sort'])} 个")
    print(f"36Kr发布 (krPublish): {len(config['krPublish'])} 个")
    print(f"融资项目 (isFinancingProject): {len(config['isFinancingProject'])} 个")
    print(f"项目标签 (labelIdList): {len(config['labelIdList'])} 个")
    print()

def main():
    """主函数"""
    # 加载配置
    config = load_parameter_config()
    
    # 打印参数摘要
    print_parameter_summary(config)
    
    # 计算总组合数
    total_combinations = calculate_total_combinations(config)
    print(f"理论总组合数: {total_combinations:,}")
    print(f"理论总组合数 (科学计数法): {total_combinations:.2e}")
    print()
    
    # 生成示例请求
    sample_requests = generate_sample_requests(config)
    
    print("API请求示例:")
    print("=" * 50)
    
    for i, request in enumerate(sample_requests, 1):
        print(f"\n示例 {i}: {request['description']}")
        print(f"请求URL: {request['endpoint']}")
        print(f"请求方法: {request['method']}")
        print("请求体参数:")
        
        # 只显示非空的参数
        params = request['body']['param']
        for key, value in params.items():
            if value is not None and value != "" and key not in ["pageNo", "pageSize", "keyword", "siteId", "platformId"]:
                print(f"  {key}: {value}")
        print("-" * 30)
    
    # 保存示例到文件
    with open("api_examples.json", 'w', encoding='utf-8') as f:
        json.dump(sample_requests, f, ensure_ascii=False, indent=2)
    
    print(f"\n示例请求已保存到 api_examples.json")
    print(f"总共生成了 {len(sample_requests)} 个示例请求")

if __name__ == "__main__":
    main()
