[{"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["2"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 消费电商"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["3"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 汽车出行"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["7"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 产业升级"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["8"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 前沿技术"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["9"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 医疗健康"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["10"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 先进制造"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["11"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 通信/半导体"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["12"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 物联网/硬件"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["13"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 工具软件"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["15"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 农林牧渔"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["16"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 能源环保"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["23"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 智能硬件"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["25"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 区块链"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["27"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 元宇宙"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": ["999"], "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "行业: 其他"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["1"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 未融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["2"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 种子轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["3"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 天使轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["4"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: Pre-A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["5"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: A轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["6"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: A+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["7"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: Pre-B轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["8"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: B轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["9"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: B+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["10"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: C轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["11"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: C+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["12"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: D轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["13"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: D+轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["14"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: E轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["16"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: F轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693153, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["18"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: G轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["19"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: H轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["24"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 股权融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["21"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 战略融资"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["26"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 定向增发"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["20"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: Pre-IPO"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["27"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 基石轮"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["25"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 已上市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["23"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: IPO"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["28"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 新三板"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["29"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 已退市/私有化"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["30"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 并购/合并"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": ["999"], "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资轮次: 其他"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2025"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2025年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2024"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2024年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2023"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2023年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2022"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2022年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2021"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2021年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2020"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2020年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2019"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2019年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2018"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2018年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2017"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2017年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2016"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2016年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2015"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2015年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2014"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2014年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2013"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2013年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2012"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2012年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2011"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2011年"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": ["2010"], "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "成立年份: 2010年及以前"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": "0", "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区: 中国"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": "1", "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "地区: 海外"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["2"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 福建省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["3"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 广东省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["5"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 北京市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["6"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 香港特别行政区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["7"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 吉林省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["9"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 天津市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["10"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 辽宁省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["13"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 上海市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["14"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 河北省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["16"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 江苏省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["18"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 内蒙古自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["31"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 台湾省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["38"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 贵州省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["46"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 宁夏回族自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["52"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 浙江省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["56"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 安徽省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["59"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 山东省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["63"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 黑龙江省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["77"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 山西省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["86"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 陕西省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["112"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 广西壮族自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["115"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 河南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["152"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 重庆市"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["156"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 四川省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["159"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 云南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["174"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 澳门特别行政区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["175"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 湖北省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["192"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 西藏自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["232"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 甘肃省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["247"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 海南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["287"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 新疆维吾尔自治区"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["301"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 青海省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["321"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 湖南省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": ["359"], "keyword": "", "siteId": 1, "platformId": 2}}, "description": "省份: 江西省"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": "3", "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "排序: 项目推荐"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": "1", "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "排序: 最近更新"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": "2", "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "排序: 最新收录"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": "1", "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "36Kr发布: 是"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": "0", "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "36Kr发布: 否"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": "1", "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资项目: 是"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": null, "krPublish": null, "isFinancingProject": "0", "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "融资项目: 否"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530084104"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 专精特新小巨人"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530018562"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 专精特新"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2076252066686472"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 创新型中小企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530280704"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 高新技术企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530297091"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 科技型中小企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530198787"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 独角兽"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530215175"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 瞪羚企业"}, {"endpoint": "https://gateway.36kr.com/api/pms/project/list", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}", "Origin": "https://pitchhub.36kr.com", "Referer": "https://pitchhub.36kr.com/"}, "body": {"partner_id": "web", "timestamp": 1756198693154, "partner_version": "1.0.0", "param": {"pageNo": "1", "pageSize": "20", "sort": null, "financingRoundIdList": null, "ifOverseas": null, "labelIdList": ["2074768530247942"], "krPublish": null, "isFinancingProject": null, "tradeIdList": null, "establishYearList": null, "provinceIdList": null, "keyword": "", "siteId": 1, "platformId": 2}}, "description": "项目标签: 雏鹰企业"}]