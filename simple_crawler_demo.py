#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版企业数据爬虫演示
展示核心功能和使用方法
"""

import json
import requests
import time
import os
from typing import Dict, List, Tuple

class SimpleCrawlerDemo:
    def __init__(self):
        """初始化演示爬虫"""
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Origin": "https://pitchhub.36kr.com",
            "Referer": "https://pitchhub.36kr.com/",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
        })

    def load_sample_combinations(self, count: int = 10) -> List[Dict]:
        """加载样本组合"""
        combinations = []
        
        # 加载第一个海外文件
        overseas_file = "all_api_combinations/overseas_chunk_0001.json"
        if os.path.exists(overseas_file):
            with open(overseas_file, 'r', encoding='utf-8') as f:
                overseas_data = json.load(f)
                combinations.extend(overseas_data[:count//2])
        
        # 加载第一个中国文件
        domestic_file = "all_api_combinations/domestic_chunk_0001.json"
        if os.path.exists(domestic_file):
            with open(domestic_file, 'r', encoding='utf-8') as f:
                domestic_data = json.load(f)
                combinations.extend(domestic_data[:count//2])
        
        return combinations[:count]

    def get_combination_info(self, api_request: Dict) -> Tuple[int, int, str]:
        """获取组合的企业总数和页数"""
        try:
            # 更新时间戳和页码
            api_request["body"]["timestamp"] = int(time.time() * 1000)
            api_request["body"]["param"]["pageNo"] = "1"
            
            response = self.session.post(
                api_request["endpoint"],
                headers=api_request["headers"],
                json=api_request["body"],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data:
                    page_info = data["data"].get("page", {})
                    total_count = page_info.get("totalCount", 0)
                    total_pages = page_info.get("totalPage", 0)
                    return total_count, total_pages, "success"
                else:
                    return 0, 0, f"API错误: {data.get('code', 'unknown')}"
            else:
                return 0, 0, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            return 0, 0, f"请求异常: {str(e)}"

    def demo_scan_combinations(self):
        """演示扫描组合功能"""
        print("🔍 演示：扫描API组合获取企业总数")
        print("="*60)
        
        # 加载样本组合
        combinations = self.load_sample_combinations(10)
        
        if not combinations:
            print("❌ 无法加载API组合文件")
            print("请确保 all_api_combinations/ 目录存在且包含数据文件")
            return
        
        print(f"📊 扫描 {len(combinations)} 个样本组合...")
        print()
        
        results = []
        high_count_combinations = []
        
        for i, combination in enumerate(combinations):
            print(f"扫描 {i+1}/{len(combinations)}: {combination.get('combination_id', 'N/A')}")
            
            # 获取组合信息
            total_count, total_pages, status = self.get_combination_info(combination)
            
            result = {
                "combination_id": combination.get("combination_id", f"demo_{i}"),
                "description": combination.get("description", ""),
                "category": combination.get("category", ""),
                "total_count": total_count,
                "total_pages": total_pages,
                "status": status
            }
            
            results.append(result)
            
            # 显示结果
            if total_count > 0:
                print(f"  ✅ 企业数: {total_count:,}, 页数: {total_pages}")
                if total_count > 1000:
                    print(f"  🔥 高数量组合! (>1000企业)")
                    high_count_combinations.append(result)
            else:
                print(f"  ⚪ 无数据 - {status}")
            
            print()
            time.sleep(0.5)  # 控制请求频率
        
        # 生成摘要报告
        self.generate_demo_report(results, high_count_combinations)

    def generate_demo_report(self, results: List[Dict], high_count_combinations: List[Dict]):
        """生成演示报告"""
        print("📊 扫描结果摘要")
        print("="*60)
        
        total_combinations = len(results)
        with_data = len([r for r in results if r["total_count"] > 0])
        no_data = len([r for r in results if r["total_count"] == 0])
        high_count = len(high_count_combinations)
        total_enterprises = sum(r["total_count"] for r in results)
        
        print(f"总组合数: {total_combinations}")
        print(f"有数据组合: {with_data}")
        print(f"无数据组合: {no_data}")
        print(f"高数量组合 (>1000): {high_count}")
        print(f"预计企业总数: {total_enterprises:,}")
        
        if high_count_combinations:
            print(f"\n🔥 高数量组合详情:")
            for combo in high_count_combinations:
                desc = combo["description"][:50] + "..." if len(combo["description"]) > 50 else combo["description"]
                print(f"  - {desc} ({combo['total_count']:,} 企业)")
        
        # 保存演示报告
        os.makedirs("demo_results", exist_ok=True)
        report = {
            "demo_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {
                "total_combinations": total_combinations,
                "with_data": with_data,
                "no_data": no_data,
                "high_count": high_count,
                "total_enterprises": total_enterprises
            },
            "results": results,
            "high_count_combinations": high_count_combinations
        }
        
        with open("demo_results/scan_demo_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 详细报告已保存: demo_results/scan_demo_report.json")

    def demo_pagination_crawl(self):
        """演示分页爬取功能"""
        print("\n📄 演示：分页爬取企业数据")
        print("="*60)
        
        # 找一个有数据的组合
        combinations = self.load_sample_combinations(20)
        
        target_combination = None
        for combination in combinations:
            total_count, total_pages, status = self.get_combination_info(combination)
            if total_count > 0:
                target_combination = combination
                print(f"找到有数据的组合: {combination.get('description', 'N/A')}")
                print(f"企业总数: {total_count:,}")
                print(f"总页数: {total_pages}")
                break
            time.sleep(0.3)
        
        if not target_combination:
            print("❌ 没有找到有数据的组合")
            return
        
        # 演示爬取前3页数据
        print(f"\n📊 演示爬取前3页数据...")
        
        all_enterprises = []
        max_pages = min(3, total_pages)
        
        for page_no in range(1, max_pages + 1):
            print(f"\n爬取第 {page_no} 页...")
            
            # 设置页码
            target_combination["body"]["param"]["pageNo"] = str(page_no)
            target_combination["body"]["timestamp"] = int(time.time() * 1000)
            
            try:
                response = self.session.post(
                    target_combination["endpoint"],
                    headers=target_combination["headers"],
                    json=target_combination["body"],
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == 0 and "data" in data:
                        projects = data["data"].get("projectList", [])
                        all_enterprises.extend(projects)
                        
                        print(f"  ✅ 获取 {len(projects)} 个企业")
                        
                        # 显示前3个企业信息
                        for i, project in enumerate(projects[:3]):
                            print(f"    {i+1}. {project.get('name', 'N/A')} - {project.get('companyName', 'N/A')}")
                    else:
                        print(f"  ❌ API错误: {data}")
                else:
                    print(f"  ❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 请求异常: {str(e)}")
            
            time.sleep(0.5)
        
        # 生成爬取报告
        print(f"\n📊 分页爬取结果:")
        print(f"总计爬取: {len(all_enterprises)} 个企业")
        print(f"爬取页数: {max_pages}/{total_pages}")
        
        if all_enterprises:
            # 保存企业数据
            with open("demo_results/enterprise_sample.json", 'w', encoding='utf-8') as f:
                json.dump(all_enterprises, f, ensure_ascii=False, indent=2)
            print(f"📁 企业数据已保存: demo_results/enterprise_sample.json")

def show_crawler_architecture():
    """显示爬虫架构说明"""
    print("\n🏗️ 企业数据爬虫架构说明")
    print("="*60)
    
    print("""
📋 核心功能模块:

1. 🔍 组合扫描器 (Combination Scanner)
   - 扫描7,526,400个API参数组合
   - 获取每个组合的企业总数 (totalCount)
   - 计算需要爬取的页数 (totalPage)
   - 标记高数量组合 (>1000企业)

2. 📄 分页爬取器 (Pagination Crawler)
   - 根据totalPage逐页爬取数据
   - 每页最多20个企业
   - 自动处理分页参数 (pageNo: 1 到 totalPage)

3. 💾 数据存储器 (Data Storage)
   - SQLite数据库存储进度和企业数据
   - 支持断点续传
   - 自动去重和数据验证

4. 📊 进度监控器 (Progress Monitor)
   - 实时跟踪爬取进度
   - 生成详细报告
   - 错误监控和重试机制

5. 🔄 并发控制器 (Concurrency Controller)
   - 多线程并发爬取
   - 请求频率控制
   - 优雅停止和暂停

📊 数据流程:
API组合 → 扫描总数 → 分页爬取 → 数据存储 → 进度跟踪 → 报告生成

⚡ 性能优化:
- 跳过无数据组合，节省时间
- 优先处理高数量组合
- 智能重试失败请求
- 内存优化和批量处理

🛡️ 稳定性保障:
- 断点续传，随时恢复
- 异常处理和日志记录
- 数据完整性验证
- 优雅退出机制
    """)

def main():
    """主函数"""
    print("🚀 36KR企业数据爬虫 - 功能演示")
    print("="*60)
    print("1. 演示扫描组合功能")
    print("2. 演示分页爬取功能")
    print("3. 查看爬虫架构说明")
    print("4. 启动完整爬虫")
    print("5. 退出")
    
    demo = SimpleCrawlerDemo()
    
    while True:
        choice = input("\n请选择演示项目 (1-5): ").strip()
        
        if choice == "1":
            demo.demo_scan_combinations()
        elif choice == "2":
            demo.demo_pagination_crawl()
        elif choice == "3":
            show_crawler_architecture()
        elif choice == "4":
            print("\n🚀 启动完整爬虫...")
            print("运行命令: python enterprise_data_crawler.py")
            break
        elif choice == "5":
            print("👋 退出演示")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
