#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输入验证修复
"""

def test_input_validation():
    """测试输入验证逻辑"""
    print("🧪 测试输入验证修复")
    print("="*50)
    
    # 模拟输入验证逻辑
    def validate_max_workers(input_str):
        """验证并发数输入"""
        if not input_str:
            return 3, "使用默认值"
        elif input_str.isdigit():
            max_workers = int(input_str)
            if 1 <= max_workers <= 20:
                if max_workers > 10:
                    return max_workers, f"高并发({max_workers})，需要确认"
                else:
                    return max_workers, "有效输入"
            else:
                return None, "超出范围(1-20)"
        else:
            return None, "无效数字"
    
    # 测试用例
    test_cases = [
        ("", "空输入"),
        ("3", "正常输入"),
        ("5", "推荐范围"),
        ("10", "边界值"),
        ("15", "高并发"),
        ("80", "您的输入"),
        ("0", "无效范围"),
        ("25", "超出最大值"),
        ("abc", "非数字"),
        ("-5", "负数")
    ]
    
    print("测试结果:")
    print("-" * 50)
    
    for input_val, description in test_cases:
        result, message = validate_max_workers(input_val)
        status = "✅" if result is not None else "❌"
        print(f"{status} 输入: '{input_val}' ({description}) → 结果: {result}, 说明: {message}")
    
    print("\n🔧 修复说明:")
    print("- 现在正确处理所有输入范围")
    print("- 超过10的并发数会要求确认")
    print("- 最大支持20个并发")
    print("- 提供清晰的错误提示")

def simulate_user_interaction():
    """模拟用户交互"""
    print("\n🎮 模拟用户交互")
    print("="*50)
    
    # 模拟您的输入场景
    print("场景1: 用户输入80")
    print("原来的逻辑:")
    print("  max_workers.isdigit() = True")
    print("  1 <= 80 <= 10 = False")
    print("  结果: 使用默认值3 ❌")
    
    print("\n修复后的逻辑:")
    print("  max_workers.isdigit() = True")
    print("  1 <= 80 <= 20 = False")
    print("  结果: 提示'超出范围(1-20)'，要求重新输入 ✅")
    
    print("\n场景2: 用户输入15")
    print("修复后的逻辑:")
    print("  max_workers.isdigit() = True")
    print("  1 <= 15 <= 20 = True")
    print("  15 > 10 = True")
    print("  结果: 要求确认是否使用高并发 ✅")

def show_new_interface():
    """显示新的交互界面"""
    print("\n🎯 新的用户界面")
    print("="*50)
    
    print("""
现在的交互流程:

1. 📊 配置扫描参数:
   请输入扫描并发数 (默认3, 建议1-5, 最大20): 80
   ❌ 并发数必须在1-20之间，请重新输入
   请输入扫描并发数 (默认3, 建议1-5, 最大20): 15
   ⚠️ 您输入了15个并发，这可能对服务器造成压力。确认使用吗？(y/N): y
   
   请输入批次大小 (默认1000): 
   
   开始扫描所有组合 (并发: 15, 批次: 1000)...
   💡 提示: 可随时按 Ctrl+C 暂停，选择菜单项6恢复

特点:
✅ 正确处理所有输入
✅ 清晰的错误提示
✅ 高并发确认机制
✅ 支持更大的并发数(最大20)
✅ 循环输入直到有效
    """)

def main():
    """主函数"""
    print("🔧 输入验证修复测试")
    print("="*50)
    
    test_input_validation()
    simulate_user_interaction()
    show_new_interface()
    
    print("\n🎉 修复完成!")
    print("现在您可以:")
    print("1. 输入1-20之间的任何并发数")
    print("2. 获得清晰的错误提示")
    print("3. 高并发时会要求确认")
    print("4. 循环输入直到有效")

if __name__ == "__main__":
    main()
