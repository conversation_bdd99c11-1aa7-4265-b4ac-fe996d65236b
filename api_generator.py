#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
36KR API Request Generator
生成所有可能的API请求组合
"""

import json
import itertools
from typing import Dict, List, Any
import os
from datetime import datetime
import time

class APIRequestGenerator:
    def __init__(self):
        # 定义所有参数选项
        self.parameters = {
            "industryList": [
                {"code": 1, "name": "文化娱乐"},
                {"code": 2, "name": "消费电商"},
                {"code": 3, "name": "汽车出行"},
                {"code": 4, "name": "教育"},
                {"code": 5, "name": "金融"},
                {"code": 6, "name": "企业服务"},
                {"code": 7, "name": "产业升级"},
                {"code": 8, "name": "前沿技术"},
                {"code": 9, "name": "医疗健康"},
                {"code": 10, "name": "先进制造"},
                {"code": 11, "name": "通信/半导体"},
                {"code": 12, "name": "物联网/硬件"},
                {"code": 13, "name": "工具软件"},
                {"code": 14, "name": "社交网络"},
                {"code": 15, "name": "农林牧渔"},
                {"code": 16, "name": "能源环保"},
                {"code": 17, "name": "本地生活"},
                {"code": 18, "name": "体育游戏"},
                {"code": 19, "name": "跨境出海"},
                {"code": 20, "name": "房产地产"},
                {"code": 21, "name": "旅游"},
                {"code": 22, "name": "广告营销"},
                {"code": 23, "name": "智能硬件"},
                {"code": 24, "name": "物流"},
                {"code": 25, "name": "区块链"},
                {"code": 26, "name": "传统制造"},
                {"code": 27, "name": "元宇宙"},
                {"code": 999, "name": "其他"}
            ],
            "financingRoundList": [
                {"code": 1, "name": "未融资"},
                {"code": 2, "name": "种子轮"},
                {"code": 3, "name": "天使轮"},
                {"code": 4, "name": "Pre-A轮"},
                {"code": 5, "name": "A轮"},
                {"code": 6, "name": "A+轮"},
                {"code": 7, "name": "Pre-B轮"},
                {"code": 8, "name": "B轮"},
                {"code": 9, "name": "B+轮"},
                {"code": 10, "name": "C轮"},
                {"code": 11, "name": "C+轮"},
                {"code": 12, "name": "D轮"},
                {"code": 13, "name": "D+轮"},
                {"code": 14, "name": "E轮"},
                {"code": 16, "name": "F轮"},
                {"code": 18, "name": "G轮"},
                {"code": 19, "name": "H轮"},
                {"code": 24, "name": "股权融资"},
                {"code": 21, "name": "战略融资"},
                {"code": 26, "name": "定向增发"},
                {"code": 20, "name": "Pre-IPO"},
                {"code": 27, "name": "基石轮"},
                {"code": 25, "name": "已上市"},
                {"code": 23, "name": "IPO"},
                {"code": 28, "name": "新三板"},
                {"code": 29, "name": "已退市/私有化"},
                {"code": 30, "name": "并购/合并"},
                {"code": 999, "name": "其他"}
            ],
            "establishTimeList": [
                {"code": 2025, "name": "2025年"},
                {"code": 2024, "name": "2024年"},
                {"code": 2023, "name": "2023年"},
                {"code": 2022, "name": "2022年"},
                {"code": 2021, "name": "2021年"},
                {"code": 2020, "name": "2020年"},
                {"code": 2019, "name": "2019年"},
                {"code": 2018, "name": "2018年"},
                {"code": 2017, "name": "2017年"},
                {"code": 2016, "name": "2016年"},
                {"code": 2015, "name": "2015年"},
                {"code": 2014, "name": "2014年"},
                {"code": 2013, "name": "2013年"},
                {"code": 2012, "name": "2012年"},
                {"code": 2011, "name": "2011年"},
                {"code": 2010, "name": "2010年及以前"}
            ],
            "ifOverseasList": [
                {"code": 0, "name": "中国"},
                {"code": 1, "name": "海外"}
            ],
            "provinceList": [
                {"code": 2, "name": "福建省"},
                {"code": 3, "name": "广东省"},
                {"code": 5, "name": "北京市"},
                {"code": 6, "name": "香港特别行政区"},
                {"code": 7, "name": "吉林省"},
                {"code": 9, "name": "天津市"},
                {"code": 10, "name": "辽宁省"},
                {"code": 13, "name": "上海市"},
                {"code": 14, "name": "河北省"},
                {"code": 16, "name": "江苏省"},
                {"code": 18, "name": "内蒙古自治区"},
                {"code": 31, "name": "台湾省"},
                {"code": 38, "name": "贵州省"},
                {"code": 46, "name": "宁夏回族自治区"},
                {"code": 52, "name": "浙江省"},
                {"code": 56, "name": "安徽省"},
                {"code": 59, "name": "山东省"},
                {"code": 63, "name": "黑龙江省"},
                {"code": 77, "name": "山西省"},
                {"code": 86, "name": "陕西省"},
                {"code": 112, "name": "广西壮族自治区"},
                {"code": 115, "name": "河南省"},
                {"code": 152, "name": "重庆市"},
                {"code": 156, "name": "四川省"},
                {"code": 159, "name": "云南省"},
                {"code": 174, "name": "澳门特别行政区"},
                {"code": 175, "name": "湖北省"},
                {"code": 192, "name": "西藏自治区"},
                {"code": 232, "name": "甘肃省"},
                {"code": 247, "name": "海南省"},
                {"code": 287, "name": "新疆维吾尔自治区"},
                {"code": 301, "name": "青海省"},
                {"code": 321, "name": "湖南省"},
                {"code": 359, "name": "江西省"}
            ],
            "sortList": [
                {"code": 3, "name": "项目推荐"},
                {"code": 1, "name": "最近更新"},
                {"code": 2, "name": "最新收录"}
            ],
            "krPublishList": [
                {"code": 1, "name": "是"},
                {"code": 0, "name": "否"}
            ],
            "isFinancingProjectList": [
                {"code": 1, "name": "是"},
                {"code": 0, "name": "否"}
            ],
            "projectLabelList": [
                {"code": 2074768530084104, "name": "专精特新小巨人"},
                {"code": 2074768530018562, "name": "专精特新"},
                {"code": 2076252066686472, "name": "创新型中小企业"},
                {"code": 2074768530280704, "name": "高新技术企业"},
                {"code": 2074768530297091, "name": "科技型中小企业"},
                {"code": 2074768530198787, "name": "独角兽"},
                {"code": 2074768530215175, "name": "瞪羚企业"},
                {"code": 2074768530247942, "name": "雏鹰企业"}
            ]
        }
        
        # 字段映射（参数列表键 -> 请求体字段名）
        self.field_mapping = {
            # list 类型 → 单元素列表或多元素列表
            "industryList": ("tradeIdList", "list"),
            "financingRoundList": ("financingRoundIdList", "list"),
            "establishTimeList": ("establishYearList", "list"),
            "provinceList": ("provinceIdList", "list"),
            "projectLabelList": ("labelIdList", "list"),
            # single 类型 → 字符串
            "ifOverseasList": ("ifOverseas", "single"),
            "sortList": ("sort", "single"),
            "krPublishList": ("krPublish", "single"),
            "isFinancingProjectList": ("isFinancingProject", "single"),
        }
        
        # API模板（符合新结构：partner 字段 + param 包裹）
        self.api_template = {
            "endpoint": "https://gateway.36kr.com/api/pms/project/list",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer {token}",
                "Origin": "https://pitchhub.36kr.com",
                "Referer": "https://pitchhub.36kr.com/"
            },
            "body": {
                "partner_id": "web",
                "timestamp": 0,
                "partner_version": "1.0.0",
                "param": {
                    "pageNo": "1",
                    "pageSize": "20",
                    "sort": None,
                    "financingRoundIdList": None,
                    "ifOverseas": None,
                    "labelIdList": None,
                    "krPublish": None,
                    "isFinancingProject": None,
                    "tradeIdList": None,
                    "establishYearList": None,
                    "provinceIdList": None,
                    "keyword": "",
                    "siteId": 1,
                    "platformId": 2
                }
            }
        }

    def generate_single_param_requests(self) -> List[Dict]:
        """生成单参数API请求（按新结构写入 body.param.*）"""
        requests = []
        
        for param_name, param_list in self.parameters.items():
            for item in param_list:
                request = json.loads(json.dumps(self.api_template))
                # 设置时间戳（毫秒）
                request["body"]["timestamp"] = int(time.time() * 1000)
                field_name = self.field_mapping.get(param_name)
                if field_name:
                    # 映射到新结构 param 下的字段
                    target, ftype = field_name
                    code_str = str(item["code"])
                    if ftype == "list":
                        request["body"]["param"][target] = [code_str]
                    else:
                        request["body"]["param"][target] = code_str
                    request["description"] = f"单参数查询: {item['name']}"
                    requests.append(request)
        
        return requests

    def generate_common_combinations(self) -> List[Dict]:
        """生成常见的参数组合"""
        combinations = []
        
        # 行业 + 融资轮次
        for industry in self.parameters["industryList"][:5]:  # 取前5个行业
            for financing in self.parameters["financingRoundList"][:5]:  # 取前5个融资轮次
                request = json.loads(json.dumps(self.api_template))
                request["body"]["timestamp"] = int(time.time() * 1000)
                request["body"]["param"]["tradeIdList"] = [str(industry["code"])]
                request["body"]["param"]["financingRoundIdList"] = [str(financing["code"])]
                request["description"] = f"行业+融资轮次: {industry['name']} + {financing['name']}"
                combinations.append(request)
        
        # 地区 + 成立时间
        for province in self.parameters["provinceList"][:5]:  # 取前5个省份
            for establish_time in self.parameters["establishTimeList"][:3]:  # 取前3个时间
                request = json.loads(json.dumps(self.api_template))
                request["body"]["timestamp"] = int(time.time() * 1000)
                request["body"]["param"]["provinceIdList"] = [str(province["code"])]
                request["body"]["param"]["establishYearList"] = [str(establish_time["code"])]
                request["description"] = f"地区+成立时间: {province['name']} + {establish_time['name']}"
                combinations.append(request)
        
        # 行业 + 地区 + 融资状态
        for industry in self.parameters["industryList"][:3]:
            for province in self.parameters["provinceList"][:3]:
                for financing_project in self.parameters["isFinancingProjectList"]:
                    request = json.loads(json.dumps(self.api_template))
                    request["body"]["timestamp"] = int(time.time() * 1000)
                    request["body"]["param"]["tradeIdList"] = [str(industry["code"])]
                    request["body"]["param"]["provinceIdList"] = [str(province["code"])]
                    request["body"]["param"]["isFinancingProject"] = str(financing_project["code"])
                    request["description"] = f"行业+地区+融资状态: {industry['name']} + {province['name']} + {financing_project['name']}"
                    combinations.append(request)
        
        return combinations

    def generate_complex_combinations(self, max_combinations: int = 100) -> List[Dict]:
        """生成复杂的多参数组合"""
        combinations = []
        
        # 选择部分参数进行组合，避免组合数量过大
        selected_params = {
            "industry": self.parameters["industryList"][:3],
            "financingRound": self.parameters["financingRoundList"][:3],
            "province": self.parameters["provinceList"][:3],
            "sort": self.parameters["sortList"],
            "krPublish": self.parameters["krPublishList"]
        }
        
        # 生成笛卡尔积组合
        param_names = list(selected_params.keys())
        param_values = list(selected_params.values())
        
        count = 0
        for combination in itertools.product(*param_values):
            if count >= max_combinations:
                break
                
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            description_parts = []
            
            for i, param_name in enumerate(param_names):
                # 将旧的 param_name 映射到新结构下字段
                reverse_lookup = {
                    "industry": ("industryList",),
                    "financingRound": ("financingRoundList",),
                    "province": ("provinceList",),
                    "sort": ("sortList",),
                    "krPublish": ("krPublishList",),
                }
                list_key = reverse_lookup[param_name][0]
                target, ftype = self.field_mapping[list_key]
                code_str = str(combination[i]["code"])
                if ftype == "list":
                    request["body"]["param"][target] = [code_str]
                else:
                    request["body"]["param"][target] = code_str
                description_parts.append(combination[i]["name"])
            
            request["description"] = f"复杂组合: {' + '.join(description_parts)}"
            combinations.append(request)
            count += 1
        
        return combinations

    def iter_full_combinations(self):
        """逐条生成全量参数组合（生成器，避免一次性占用内存；写入 body.param.*）"""
        # 使用参数列表键，结合 field_mapping 进行映射
        ordered = [
            "industryList",
            "financingRoundList",
            "establishTimeList",
            "ifOverseasList",
            "provinceList",
            "sortList",
            "krPublishList",
            "isFinancingProjectList",
            "projectLabelList",
        ]
        lists = [self.parameters[list_key] for list_key in ordered]
        for combo in itertools.product(*lists):
            request = json.loads(json.dumps(self.api_template))
            request["body"]["timestamp"] = int(time.time() * 1000)
            desc_parts = []
            for i, list_key in enumerate(ordered):
                target, ftype = self.field_mapping[list_key]
                code_str = str(combo[i]["code"])
                if ftype == "list":
                    request["body"]["param"][target] = [code_str]
                else:
                    request["body"]["param"][target] = code_str
                desc_parts.append(combo[i]["name"])
            request["description"] = f"全量组合: {' + '.join(desc_parts)}"
            yield request

    def count_full_combinations(self) -> int:
        """计算全量组合理论总数（笛卡尔积规模）"""
        ordered = [
            "industryList",
            "financingRoundList",
            "establishTimeList",
            "ifOverseasList",
            "provinceList",
            "sortList",
            "krPublishList",
            "isFinancingProjectList",
            "projectLabelList",
        ]
        total = 1
        for key in ordered:
            total *= len(self.parameters[key])
        return total

    def save_full_combinations(self, output_dir: str = "api_requests", chunk_size: int = 5000, limit: int = None):
        """
        生成并分片保存全量组合到多个文件，避免内存和单文件过大。
        - chunk_size: 每个文件包含的请求条数
        - limit: 可选，限制生成的总条数（用于抽样或测试）
        生成的文件命名为 full_combinations_part_001.json, ...
        并写入 full_combinations_manifest.json 清单与 statistics.json 统计。
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        total_possible = self.count_full_combinations()
        chunk = []
        file_idx = 0
        generated = 0
        files = []

        for req in self.iter_full_combinations():
            if limit is not None and generated >= limit:
                break
            chunk.append(req)
            generated += 1
            if len(chunk) >= chunk_size:
                file_idx += 1
                filename = os.path.join(output_dir, f"full_combinations_part_{file_idx:03d}.json")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(chunk, f, ensure_ascii=False, indent=2)
                print(f"已生成分片 {file_idx}: {len(chunk)} 条 -> {filename}")
                files.append(os.path.basename(filename))
                chunk = []

        # 写入最后未写出的分片
        if chunk:
            file_idx += 1
            filename = os.path.join(output_dir, f"full_combinations_part_{file_idx:03d}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(chunk, f, ensure_ascii=False, indent=2)
            print(f"已生成分片 {file_idx}: {len(chunk)} 条 -> {filename}")
            files.append(os.path.basename(filename))

        # 写 manifest 清单
        manifest = {
            "created_at": datetime.utcnow().isoformat() + "Z",
            "chunk_size": chunk_size,
            "total_generated": generated,
            "total_possible": total_possible,
            "num_files": len(files),
            "files": files,
            "note": "为避免爆炸式规模，默认未在 main 中自动生成；可通过显式调用或环境变量触发。",
        }
        manifest_filename = os.path.join(output_dir, "full_combinations_manifest.json")
        with open(manifest_filename, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, ensure_ascii=False, indent=2)
        print(f"已写入清单: {manifest_filename}")

        # 更新/合并 statistics.json
        stats_filename = os.path.join(output_dir, "statistics.json")
        if os.path.exists(stats_filename):
            with open(stats_filename, 'r', encoding='utf-8') as f:
                try:
                    stats = json.load(f)
                except Exception:
                    stats = {}
        else:
            stats = {}

        stats.setdefault("categories", {})
        stats["categories"]["full_combinations_chunks"] = len(files)
        stats["categories"]["full_combinations_total_generated"] = generated
        stats["full_combinations_total_possible"] = total_possible

        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"已更新统计: {stats_filename}")

    def generate_all_requests(self) -> Dict[str, List[Dict]]:
        """生成所有类型的API请求"""
        return {
            "single_param_requests": self.generate_single_param_requests(),
            "common_combinations": self.generate_common_combinations(),
            "complex_combinations": self.generate_complex_combinations()
        }

    def save_requests_to_files(self, output_dir: str = "api_requests"):
        """保存所有请求到文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        all_requests = self.generate_all_requests()
        
        for category, requests in all_requests.items():
            filename = os.path.join(output_dir, f"{category}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
            print(f"已生成 {len(requests)} 个 {category} 请求，保存到 {filename}")
        
        # 生成统计信息
        total_requests = sum(len(requests) for requests in all_requests.values())
        stats = {
            "total_requests": total_requests,
            "categories": {category: len(requests) for category, requests in all_requests.items()},
            "parameter_counts": {param: len(values) for param, values in self.parameters.items()}
        }
        
        stats_filename = os.path.join(output_dir, "statistics.json")
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n统计信息:")
        print(f"总请求数: {total_requests}")
        for category, count in stats["categories"].items():
            print(f"  {category}: {count}")

def main():
    """主函数"""
    generator = APIRequestGenerator()
    
    print("36KR API请求生成器")
    print("=" * 50)
    
    # 生成并保存所有请求
    generator.save_requests_to_files()
    
    # 如需生成全量组合，请设置环境变量或在外部显式调用：
    #   os.environ["FULL_COMBINATIONS"] = "1"
    #   可选：os.environ["FULL_CHUNK_SIZE"] = "5000"，os.environ["FULL_LIMIT"] = "100000"
    full_flag = os.environ.get("FULL_COMBINATIONS", "").lower() in ("1", "true", "yes")
    if full_flag:
        try:
            chunk_size = int(os.environ.get("FULL_CHUNK_SIZE", "5000"))
        except ValueError:
            chunk_size = 5000
        try:
            limit_env = os.environ.get("FULL_LIMIT")
            limit = int(limit_env) if limit_env is not None else None
        except ValueError:
            limit = None
        print(f"\n开始生成全量组合（chunk_size={chunk_size}, limit={limit}）...")
        generator.save_full_combinations(chunk_size=chunk_size, limit=limit)
    
    print("\n生成完成！")
    print("文件保存在 api_requests/ 目录下")

if __name__ == "__main__":
    main()
