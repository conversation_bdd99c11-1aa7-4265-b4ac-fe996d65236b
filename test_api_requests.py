#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API请求脚本
"""

import json
import requests
import time
from typing import Dict, List

def load_api_examples(filename: str = "api_examples.json") -> List[Dict]:
    """加载API示例"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def test_api_request(request_data: Dict, timeout: int = 10) -> Dict:
    """测试单个API请求"""
    try:
        # 更新时间戳
        request_data["body"]["timestamp"] = int(time.time() * 1000)
        
        # 发送请求
        response = requests.post(
            request_data["endpoint"],
            headers=request_data["headers"],
            json=request_data["body"],
            timeout=timeout
        )
        
        return {
            "success": True,
            "status_code": response.status_code,
            "response_size": len(response.text),
            "response_data": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text[:500],
            "description": request_data.get("description", "")
        }
        
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": str(e),
            "description": request_data.get("description", "")
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "description": request_data.get("description", "")
        }

def generate_curl_command(request_data: Dict) -> str:
    """生成curl命令"""
    headers = []
    for key, value in request_data["headers"].items():
        if "{token}" not in value:  # 跳过需要token的header
            headers.append(f'-H "{key}: {value}"')
    
    # 更新时间戳
    request_data["body"]["timestamp"] = int(time.time() * 1000)
    
    curl_cmd = f"""curl -X {request_data["method"]} \\
  {request_data["endpoint"]} \\
  {' '.join(headers)} \\
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \\
  -d '{json.dumps(request_data["body"], ensure_ascii=False)}'"""
    
    return curl_cmd

def main():
    """主函数"""
    print("36KR API请求测试工具")
    print("=" * 50)
    
    # 加载示例请求
    try:
        examples = load_api_examples()
        print(f"加载了 {len(examples)} 个API请求示例")
    except FileNotFoundError:
        print("错误: 找不到 api_examples.json 文件")
        print("请先运行 generate_api_examples.py 生成示例文件")
        return
    
    print("\n选择测试模式:")
    print("1. 生成curl命令 (推荐)")
    print("2. 直接测试API (需要有效token)")
    
    choice = input("\n请选择 (1 或 2): ").strip()
    
    if choice == "1":
        print("\n生成的curl命令:")
        print("=" * 50)
        
        for i, example in enumerate(examples, 1):
            print(f"\n示例 {i}: {example['description']}")
            print("-" * 30)
            curl_cmd = generate_curl_command(example)
            print(curl_cmd)
            print()
        
        print("注意: 请将 YOUR_TOKEN_HERE 替换为实际的认证token")
        
    elif choice == "2":
        token = input("\n请输入您的认证token: ").strip()
        if not token:
            print("错误: 需要提供有效的token")
            return
        
        # 更新所有请求的token
        for example in examples:
            example["headers"]["Authorization"] = f"Bearer {token}"
        
        print("\n开始测试API请求...")
        print("=" * 50)
        
        results = []
        for i, example in enumerate(examples, 1):
            print(f"\n测试 {i}/{len(examples)}: {example['description']}")
            result = test_api_request(example)
            results.append(result)
            
            if result["success"]:
                print(f"✓ 成功 - 状态码: {result['status_code']}, 响应大小: {result['response_size']} 字节")
                # 显示部分响应数据
                if isinstance(result["response_data"], dict):
                    if "projectList" in result["response_data"]:
                        project_count = len(result["response_data"]["projectList"])
                        print(f"  返回项目数量: {project_count}")
                    if "page" in result["response_data"]:
                        page_info = result["response_data"]["page"]
                        print(f"  分页信息: 第{page_info.get('pageNo', 'N/A')}页, 总计{page_info.get('totalCount', 'N/A')}条")
            else:
                print(f"✗ 失败 - 错误: {result['error']}")
            
            # 避免请求过于频繁
            time.sleep(1)
        
        # 生成测试报告
        successful = sum(1 for r in results if r["success"])
        print(f"\n测试完成!")
        print(f"成功: {successful}/{len(results)}")
        print(f"失败: {len(results) - successful}/{len(results)}")
        
        # 保存测试结果
        with open("test_results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print("详细测试结果已保存到 test_results.json")
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
