#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试企业数据爬虫的功能
"""

import json
import os
from enterprise_data_crawler import EnterpriseDataCrawler

def test_crawler_basic_functions():
    """测试爬虫基本功能"""
    print("🧪 测试企业数据爬虫基本功能")
    print("="*60)
    
    # 初始化爬虫
    crawler = EnterpriseDataCrawler(output_dir="test_crawl_results")
    
    print("✅ 爬虫初始化成功")
    
    # 测试加载API组合
    print("\n📁 测试加载API组合...")
    combinations = crawler.load_api_combinations()
    print(f"✅ 加载了 {len(combinations)} 个API组合")
    
    if combinations:
        # 测试获取单个组合的总数
        print("\n🔍 测试获取组合总数...")
        test_combination = combinations[0]
        total_count, total_pages, status = crawler.get_total_count_for_combination(test_combination)
        
        print(f"测试组合: {test_combination.get('description', 'N/A')}")
        print(f"总企业数: {total_count}")
        print(f"总页数: {total_pages}")
        print(f"状态: {status}")
        
        if total_count > 0:
            print("✅ 成功获取到企业数据")
        else:
            print("⚠️ 该组合没有企业数据")
    
    # 测试数据库功能
    print("\n💾 测试数据库功能...")
    status = crawler.get_crawl_status()
    print(f"✅ 数据库连接正常，当前状态: {list(status.keys())}")
    
    print("\n🎉 基本功能测试完成！")

def test_scan_sample_combinations():
    """测试扫描样本组合"""
    print("\n🔍 测试扫描样本组合")
    print("="*60)
    
    crawler = EnterpriseDataCrawler(output_dir="test_crawl_results")
    
    # 加载前10个组合进行测试
    combinations = crawler.load_api_combinations()[:10]
    
    print(f"📊 扫描前 {len(combinations)} 个组合...")
    
    results = []
    for i, combination in enumerate(combinations):
        print(f"\n扫描 {i+1}/{len(combinations)}: {combination.get('combination_id', 'N/A')}")
        
        total_count, total_pages, status = crawler.get_total_count_for_combination(combination)
        
        result = {
            "combination_id": combination.get("combination_id", f"test_{i}"),
            "description": combination.get("description", ""),
            "category": combination.get("category", ""),
            "total_count": total_count,
            "total_pages": total_pages,
            "status": status
        }
        
        results.append(result)
        
        print(f"  企业数: {total_count}, 页数: {total_pages}")
        if total_count > 1000:
            print(f"  🔥 高数量组合!")
        elif total_count == 0:
            print(f"  ⚪ 无数据")
        
        # 控制频率
        import time
        time.sleep(0.5)
    
    # 生成测试报告
    test_report = {
        "test_time": "2025-08-26",
        "total_tested": len(results),
        "results": results,
        "summary": {
            "with_data": len([r for r in results if r["total_count"] > 0]),
            "no_data": len([r for r in results if r["total_count"] == 0]),
            "high_count": len([r for r in results if r["total_count"] > 1000]),
            "total_enterprises": sum(r["total_count"] for r in results)
        }
    }
    
    # 保存测试报告
    os.makedirs("test_crawl_results", exist_ok=True)
    with open("test_crawl_results/test_scan_report.json", 'w', encoding='utf-8') as f:
        json.dump(test_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 扫描结果摘要:")
    print(f"有数据组合: {test_report['summary']['with_data']}")
    print(f"无数据组合: {test_report['summary']['no_data']}")
    print(f"高数量组合: {test_report['summary']['high_count']}")
    print(f"总企业数: {test_report['summary']['total_enterprises']:,}")
    
    print(f"\n📁 测试报告已保存: test_crawl_results/test_scan_report.json")

def test_single_page_crawl():
    """测试单页爬取功能"""
    print("\n📄 测试单页爬取功能")
    print("="*60)
    
    crawler = EnterpriseDataCrawler(output_dir="test_crawl_results")
    
    # 找一个有数据的组合
    combinations = crawler.load_api_combinations()
    
    for combination in combinations[:20]:  # 测试前20个
        total_count, total_pages, status = crawler.get_total_count_for_combination(combination)
        
        if total_count > 0:
            print(f"找到有数据的组合: {combination.get('description', 'N/A')}")
            print(f"企业数: {total_count}, 页数: {total_pages}")
            
            # 测试爬取第一页
            combination_id = combination.get("combination_id", "test_combo")
            
            # 模拟保存到数据库
            import sqlite3
            from datetime import datetime
            
            conn = sqlite3.connect(crawler.db_path)
            cursor = conn.cursor()
            
            # 插入进度记录
            cursor.execute('''
                INSERT OR REPLACE INTO crawl_progress 
                (combination_id, category, description, total_count, total_pages, 
                 completed_pages, status, last_update)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                combination_id,
                combination.get("category", "test"),
                combination.get("description", ""),
                total_count,
                total_pages,
                0,
                "pending",
                datetime.now().isoformat()
            ))
            conn.commit()
            conn.close()
            
            # 测试爬取单页
            success = crawler.crawl_single_page(combination_id, 1)
            
            if success:
                print("✅ 单页爬取成功!")
                
                # 检查数据库中的数据
                conn = sqlite3.connect(crawler.db_path)
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM enterprise_data WHERE combination_id = ?', (combination_id,))
                count = cursor.fetchone()[0]
                conn.close()
                
                print(f"📊 数据库中保存了 {count} 条企业数据")
            else:
                print("❌ 单页爬取失败")
            
            break
        
        import time
        time.sleep(0.3)
    else:
        print("⚠️ 没有找到有数据的组合")

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 企业数据爬虫使用指南")
    print("="*60)
    
    print("""
🚀 快速开始:
1. 运行 python enterprise_data_crawler.py
2. 选择 "1" 扫描所有组合 (获取每个组合的企业总数)
3. 选择 "2" 开始爬取数据
4. 选择 "5" 导出数据到CSV文件

📊 主要功能:
- 扫描7,526,400个参数组合，获取每个组合的企业总数
- 自动识别高数量组合 (>1000企业) 并标记
- 跳过无数据的组合，节省时间
- 支持分页爬取，获取完整数据
- 断点续传，可随时暂停和恢复
- 多线程并发，提高爬取效率
- 自动重试失败的请求
- 生成详细的进度报告
- 导出数据到CSV格式

⚠️ 注意事项:
- 建议先扫描所有组合，了解数据分布
- 控制并发线程数，避免对服务器造成压力
- 定期查看进度报告，监控爬取状态
- 及时导出数据，避免数据丢失

📁 输出文件:
- crawl_results/crawl_progress.db - 进度数据库
- crawl_results/data/ - 企业数据
- crawl_results/reports/ - 各种报告
- crawl_results/crawler.log - 日志文件
    """)

def main():
    """主函数"""
    print("🧪 企业数据爬虫测试工具")
    print("="*60)
    print("1. 测试基本功能")
    print("2. 测试扫描样本组合")
    print("3. 测试单页爬取")
    print("4. 显示使用指南")
    print("5. 运行完整爬虫")
    print("6. 退出")
    
    while True:
        choice = input("\n请选择测试项目 (1-6): ").strip()
        
        if choice == "1":
            test_crawler_basic_functions()
        elif choice == "2":
            test_scan_sample_combinations()
        elif choice == "3":
            test_single_page_crawl()
        elif choice == "4":
            show_usage_guide()
        elif choice == "5":
            print("\n启动完整爬虫...")
            from enterprise_data_crawler import main as crawler_main
            crawler_main()
            break
        elif choice == "6":
            print("👋 退出测试")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
