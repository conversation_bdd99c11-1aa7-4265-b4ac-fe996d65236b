36KR API 所有curl命令
==================================================

【single_param】
------------------------------

# 1. 行业: 消费电商
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["2"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 2. 行业: 汽车出行
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["3"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 3. 行业: 产业升级
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["7"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 4. 行业: 前沿技术
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["8"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 5. 行业: 医疗健康
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["9"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 6. 行业: 先进制造
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["10"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 7. 行业: 通信/半导体
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["11"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 8. 行业: 物联网/硬件
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["12"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 9. 行业: 工具软件
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["13"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 10. 行业: 农林牧渔
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["15"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 11. 行业: 能源环保
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["16"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 12. 行业: 智能硬件
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["23"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 13. 行业: 区块链
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["25"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 14. 行业: 元宇宙
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["27"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 15. 行业: 其他
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["999"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 16. 融资轮次: 未融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["1"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 17. 融资轮次: 种子轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["2"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 18. 融资轮次: 天使轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["3"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 19. 融资轮次: Pre-A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["4"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 20. 融资轮次: A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["5"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 21. 融资轮次: A+轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["6"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 22. 融资轮次: Pre-B轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["7"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 23. 融资轮次: B轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749817,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["8"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 24. 融资轮次: B+轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["9"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 25. 融资轮次: C轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["10"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 26. 融资轮次: C+轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["11"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 27. 融资轮次: D轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["12"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 28. 融资轮次: D+轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["13"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 29. 融资轮次: E轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["14"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 30. 融资轮次: F轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["16"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 31. 融资轮次: G轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["18"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 32. 融资轮次: H轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["19"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 33. 融资轮次: 股权融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["24"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 34. 融资轮次: 战略融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["21"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 35. 融资轮次: 定向增发
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["26"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 36. 融资轮次: Pre-IPO
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["20"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 37. 融资轮次: 基石轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["27"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 38. 融资轮次: 已上市
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["25"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 39. 融资轮次: IPO
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["23"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 40. 融资轮次: 新三板
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["28"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 41. 融资轮次: 已退市/私有化
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["29"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 42. 融资轮次: 并购/合并
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["30"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 43. 融资轮次: 其他
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["999"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 44. 成立年份: 2025年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2025"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 45. 成立年份: 2024年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2024"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 46. 成立年份: 2023年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2023"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 47. 成立年份: 2022年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2022"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 48. 成立年份: 2021年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2021"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 49. 成立年份: 2020年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2020"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 50. 成立年份: 2019年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2019"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 51. 成立年份: 2018年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2018"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 52. 成立年份: 2017年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2017"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 53. 成立年份: 2016年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2016"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 54. 成立年份: 2015年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2015"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 55. 成立年份: 2014年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2014"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 56. 成立年份: 2013年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2013"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 57. 成立年份: 2012年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2012"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 58. 成立年份: 2011年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2011"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 59. 成立年份: 2010年及以前
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2010"],"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 60. 地区: 中国
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":"0","labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 61. 地区: 海外
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":"1","labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 62. 省份: 福建省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 63. 省份: 广东省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 64. 省份: 北京市
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["5"],"keyword":"","siteId":1,"platformId":2}}'

# 65. 省份: 香港特别行政区
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["6"],"keyword":"","siteId":1,"platformId":2}}'

# 66. 省份: 吉林省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["7"],"keyword":"","siteId":1,"platformId":2}}'

# 67. 省份: 天津市
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["9"],"keyword":"","siteId":1,"platformId":2}}'

# 68. 省份: 辽宁省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["10"],"keyword":"","siteId":1,"platformId":2}}'

# 69. 省份: 上海市
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["13"],"keyword":"","siteId":1,"platformId":2}}'

# 70. 省份: 河北省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["14"],"keyword":"","siteId":1,"platformId":2}}'

# 71. 省份: 江苏省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["16"],"keyword":"","siteId":1,"platformId":2}}'

# 72. 省份: 内蒙古自治区
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["18"],"keyword":"","siteId":1,"platformId":2}}'

# 73. 省份: 台湾省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["31"],"keyword":"","siteId":1,"platformId":2}}'

# 74. 省份: 贵州省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["38"],"keyword":"","siteId":1,"platformId":2}}'

# 75. 省份: 宁夏回族自治区
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["46"],"keyword":"","siteId":1,"platformId":2}}'

# 76. 省份: 浙江省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["52"],"keyword":"","siteId":1,"platformId":2}}'

# 77. 省份: 安徽省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["56"],"keyword":"","siteId":1,"platformId":2}}'

# 78. 省份: 山东省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["59"],"keyword":"","siteId":1,"platformId":2}}'

# 79. 省份: 黑龙江省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["63"],"keyword":"","siteId":1,"platformId":2}}'

# 80. 省份: 山西省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["77"],"keyword":"","siteId":1,"platformId":2}}'

# 81. 省份: 陕西省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["86"],"keyword":"","siteId":1,"platformId":2}}'

# 82. 省份: 广西壮族自治区
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["112"],"keyword":"","siteId":1,"platformId":2}}'

# 83. 省份: 河南省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["115"],"keyword":"","siteId":1,"platformId":2}}'

# 84. 省份: 重庆市
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["152"],"keyword":"","siteId":1,"platformId":2}}'

# 85. 省份: 四川省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["156"],"keyword":"","siteId":1,"platformId":2}}'

# 86. 省份: 云南省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["159"],"keyword":"","siteId":1,"platformId":2}}'

# 87. 省份: 澳门特别行政区
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["174"],"keyword":"","siteId":1,"platformId":2}}'

# 88. 省份: 湖北省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["175"],"keyword":"","siteId":1,"platformId":2}}'

# 89. 省份: 西藏自治区
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["192"],"keyword":"","siteId":1,"platformId":2}}'

# 90. 省份: 甘肃省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["232"],"keyword":"","siteId":1,"platformId":2}}'

# 91. 省份: 海南省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["247"],"keyword":"","siteId":1,"platformId":2}}'

# 92. 省份: 新疆维吾尔自治区
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["287"],"keyword":"","siteId":1,"platformId":2}}'

# 93. 省份: 青海省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["301"],"keyword":"","siteId":1,"platformId":2}}'

# 94. 省份: 湖南省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["321"],"keyword":"","siteId":1,"platformId":2}}'

# 95. 省份: 江西省
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":["359"],"keyword":"","siteId":1,"platformId":2}}'

# 96. 排序: 项目推荐
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 97. 排序: 最近更新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 98. 排序: 最新收录
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 99. 36Kr发布: 是
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":"1","isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 100. 36Kr发布: 否
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":"0","isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 101. 融资项目: 是
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":"1","tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 102. 融资项目: 否
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":"0","tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 103. 项目标签: 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2074768530084104"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 104. 项目标签: 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2074768530018562"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 105. 项目标签: 创新型中小企业
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2076252066686472"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 106. 项目标签: 高新技术企业
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2074768530280704"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 107. 项目标签: 科技型中小企业
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2074768530297091"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 108. 项目标签: 独角兽
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2074768530198787"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 109. 项目标签: 瞪羚企业
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2074768530215175"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 110. 项目标签: 雏鹰企业
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":["2074768530247942"],"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'


【two_param_combinations】
------------------------------

# 1. 行业+融资: 消费电商 + 未融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["1"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["2"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 2. 行业+融资: 消费电商 + 种子轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["2"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["2"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 3. 行业+融资: 消费电商 + 天使轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["3"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["2"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 4. 行业+融资: 消费电商 + Pre-A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749818,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["4"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["2"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 5. 行业+融资: 消费电商 + A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["5"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["2"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 6. 行业+融资: 汽车出行 + 未融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["1"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["3"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 7. 行业+融资: 汽车出行 + 种子轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["2"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["3"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 8. 行业+融资: 汽车出行 + 天使轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["3"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["3"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 9. 行业+融资: 汽车出行 + Pre-A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["4"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["3"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 10. 行业+融资: 汽车出行 + A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["5"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["3"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 11. 行业+融资: 产业升级 + 未融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["1"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["7"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 12. 行业+融资: 产业升级 + 种子轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["2"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["7"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 13. 行业+融资: 产业升级 + 天使轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["3"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["7"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 14. 行业+融资: 产业升级 + Pre-A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["4"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["7"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 15. 行业+融资: 产业升级 + A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["5"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["7"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 16. 行业+融资: 前沿技术 + 未融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["1"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["8"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 17. 行业+融资: 前沿技术 + 种子轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["2"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["8"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 18. 行业+融资: 前沿技术 + 天使轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["3"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["8"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 19. 行业+融资: 前沿技术 + Pre-A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["4"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["8"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 20. 行业+融资: 前沿技术 + A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["5"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["8"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 21. 行业+融资: 医疗健康 + 未融资
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["1"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["9"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 22. 行业+融资: 医疗健康 + 种子轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["2"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["9"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 23. 行业+融资: 医疗健康 + 天使轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["3"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["9"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 24. 行业+融资: 医疗健康 + Pre-A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["4"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["9"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 25. 行业+融资: 医疗健康 + A轮
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":["5"],"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":["9"],"establishYearList":null,"provinceIdList":null,"keyword":"","siteId":1,"platformId":2}}'

# 26. 地区+年份: 福建省 + 2025年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 27. 地区+年份: 福建省 + 2024年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2024"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 28. 地区+年份: 福建省 + 2023年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2023"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 29. 地区+年份: 福建省 + 2022年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2022"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 30. 地区+年份: 福建省 + 2021年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2021"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 31. 地区+年份: 广东省 + 2025年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 32. 地区+年份: 广东省 + 2024年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2024"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 33. 地区+年份: 广东省 + 2023年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2023"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 34. 地区+年份: 广东省 + 2022年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2022"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 35. 地区+年份: 广东省 + 2021年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2021"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 36. 地区+年份: 北京市 + 2025年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2025"],"provinceIdList":["5"],"keyword":"","siteId":1,"platformId":2}}'

# 37. 地区+年份: 北京市 + 2024年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2024"],"provinceIdList":["5"],"keyword":"","siteId":1,"platformId":2}}'

# 38. 地区+年份: 北京市 + 2023年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2023"],"provinceIdList":["5"],"keyword":"","siteId":1,"platformId":2}}'

# 39. 地区+年份: 北京市 + 2022年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2022"],"provinceIdList":["5"],"keyword":"","siteId":1,"platformId":2}}'

# 40. 地区+年份: 北京市 + 2021年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2021"],"provinceIdList":["5"],"keyword":"","siteId":1,"platformId":2}}'

# 41. 地区+年份: 香港特别行政区 + 2025年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2025"],"provinceIdList":["6"],"keyword":"","siteId":1,"platformId":2}}'

# 42. 地区+年份: 香港特别行政区 + 2024年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2024"],"provinceIdList":["6"],"keyword":"","siteId":1,"platformId":2}}'

# 43. 地区+年份: 香港特别行政区 + 2023年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2023"],"provinceIdList":["6"],"keyword":"","siteId":1,"platformId":2}}'

# 44. 地区+年份: 香港特别行政区 + 2022年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2022"],"provinceIdList":["6"],"keyword":"","siteId":1,"platformId":2}}'

# 45. 地区+年份: 香港特别行政区 + 2021年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2021"],"provinceIdList":["6"],"keyword":"","siteId":1,"platformId":2}}'

# 46. 地区+年份: 吉林省 + 2025年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2025"],"provinceIdList":["7"],"keyword":"","siteId":1,"platformId":2}}'

# 47. 地区+年份: 吉林省 + 2024年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2024"],"provinceIdList":["7"],"keyword":"","siteId":1,"platformId":2}}'

# 48. 地区+年份: 吉林省 + 2023年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2023"],"provinceIdList":["7"],"keyword":"","siteId":1,"platformId":2}}'

# 49. 地区+年份: 吉林省 + 2022年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2022"],"provinceIdList":["7"],"keyword":"","siteId":1,"platformId":2}}'

# 50. 地区+年份: 吉林省 + 2021年
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":null,"financingRoundIdList":null,"ifOverseas":null,"labelIdList":null,"krPublish":null,"isFinancingProject":null,"tradeIdList":null,"establishYearList":["2021"],"provinceIdList":["7"],"keyword":"","siteId":1,"platformId":2}}'


【sample_full_combinations】
------------------------------

# 1. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 是 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 2. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 是 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 3. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 是 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 4. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 是 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 5. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 否 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 6. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 否 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 7. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 否 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 8. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 否 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 9. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 是 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 10. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 是 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 11. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 是 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 12. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 是 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 13. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 否 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 14. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 否 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 15. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 否 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 16. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最近更新 + 否 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 17. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 是 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 18. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 是 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 19. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 是 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 20. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 是 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 21. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 否 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 22. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 否 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 23. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 否 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 24. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 最新收录 + 否 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 25. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 是 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 26. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 是 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 27. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 是 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 28. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 是 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 29. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 否 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 30. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 否 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 31. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 否 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 32. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 项目推荐 + 否 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 33. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 是 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 34. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 是 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 35. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 是 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 36. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 是 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 37. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 否 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 38. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 否 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 39. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 否 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 40. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最近更新 + 否 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"1","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 41. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 是 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 42. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 是 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749819,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 43. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 是 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 44. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 是 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 45. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 否 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 46. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 否 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 47. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 否 + 否 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530084104"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 48. 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 广东省 + 最新收录 + 否 + 否 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"2","financingRoundIdList":["1"],"ifOverseas":"0","labelIdList":["2074768530018562"],"krPublish":"0","isFinancingProject":"0","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["3"],"keyword":"","siteId":1,"platformId":2}}'

# 49. 全参数组合: 消费电商 + 未融资 + 2025年 + 海外 + 福建省 + 项目推荐 + 是 + 是 + 专精特新小巨人
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"1","labelIdList":["2074768530084104"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'

# 50. 全参数组合: 消费电商 + 未融资 + 2025年 + 海外 + 福建省 + 项目推荐 + 是 + 是 + 专精特新
curl -X POST \
  'https://gateway.36kr.com/api/pms/project/list' \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN_HERE" -H "Origin: https://pitchhub.36kr.com" -H "Referer: https://pitchhub.36kr.com/" \
  -d '{"partner_id":"web","timestamp":1756198749820,"partner_version":"1.0.0","param":{"pageNo":"1","pageSize":"20","sort":"3","financingRoundIdList":["1"],"ifOverseas":"1","labelIdList":["2074768530018562"],"krPublish":"1","isFinancingProject":"1","tradeIdList":["2"],"establishYearList":["2025"],"provinceIdList":["2"],"keyword":"","siteId":1,"platformId":2}}'


