#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
36KR API请求示例代码
"""

# 示例 1: 行业: 消费电商
def example_1():
    import requests
    import json
    
    url = "https://gateway.36kr.com/api/pms/project/list"
    headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer {token}",
      "Origin": "https://pitchhub.36kr.com",
      "Referer": "https://pitchhub.36kr.com/"
    }
    data = {
      "partner_id": "web",
      "timestamp": 1756198749820,
      "partner_version": "1.0.0",
      "param": {
        "pageNo": "1",
        "pageSize": "20",
        "sort": null,
        "financingRoundIdList": null,
        "ifOverseas": null,
        "labelIdList": null,
        "krPublish": null,
        "isFinancingProject": null,
        "tradeIdList": [
          "2"
        ],
        "establishYearList": null,
        "provinceIdList": null,
        "keyword": "",
        "siteId": 1,
        "platformId": 2
      }
    }
    
    # 记得替换YOUR_TOKEN_HERE为实际的token
    headers["Authorization"] = "Bearer YOUR_TOKEN_HERE"
    
    response = requests.post(url, headers=headers, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

# 示例 2: 行业+融资: 消费电商 + 未融资
def example_2():
    import requests
    import json
    
    url = "https://gateway.36kr.com/api/pms/project/list"
    headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer {token}",
      "Origin": "https://pitchhub.36kr.com",
      "Referer": "https://pitchhub.36kr.com/"
    }
    data = {
      "partner_id": "web",
      "timestamp": 1756198749820,
      "partner_version": "1.0.0",
      "param": {
        "pageNo": "1",
        "pageSize": "20",
        "sort": null,
        "financingRoundIdList": [
          "1"
        ],
        "ifOverseas": null,
        "labelIdList": null,
        "krPublish": null,
        "isFinancingProject": null,
        "tradeIdList": [
          "2"
        ],
        "establishYearList": null,
        "provinceIdList": null,
        "keyword": "",
        "siteId": 1,
        "platformId": 2
      }
    }
    
    # 记得替换YOUR_TOKEN_HERE为实际的token
    headers["Authorization"] = "Bearer YOUR_TOKEN_HERE"
    
    response = requests.post(url, headers=headers, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

# 示例 3: 全参数组合: 消费电商 + 未融资 + 2025年 + 中国 + 福建省 + 项目推荐 + 是 + 是 + 专精特新小巨人
def example_3():
    import requests
    import json
    
    url = "https://gateway.36kr.com/api/pms/project/list"
    headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer {token}",
      "Origin": "https://pitchhub.36kr.com",
      "Referer": "https://pitchhub.36kr.com/"
    }
    data = {
      "partner_id": "web",
      "timestamp": 1756198749820,
      "partner_version": "1.0.0",
      "param": {
        "pageNo": "1",
        "pageSize": "20",
        "sort": "3",
        "financingRoundIdList": [
          "1"
        ],
        "ifOverseas": "0",
        "labelIdList": [
          "2074768530084104"
        ],
        "krPublish": "1",
        "isFinancingProject": "1",
        "tradeIdList": [
          "2"
        ],
        "establishYearList": [
          "2025"
        ],
        "provinceIdList": [
          "2"
        ],
        "keyword": "",
        "siteId": 1,
        "platformId": 2
      }
    }
    
    # 记得替换YOUR_TOKEN_HERE为实际的token
    headers["Authorization"] = "Bearer YOUR_TOKEN_HERE"
    
    response = requests.post(url, headers=headers, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

if __name__ == "__main__":
    # 运行示例（取消注释想要运行的示例）
    # example_1()
    # example_2()
    # example_3()
